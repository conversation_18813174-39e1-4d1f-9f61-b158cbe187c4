<?php

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [
    'p.name as project_name',
    'm.name as milestone_name', 
    'm.due_date',
    'm.billing_type',
    'm.rate',
    '(SELECT COUNT(t.id) FROM ' . db_prefix() . 'tasks t WHERE t.rel_type="project" AND t.rel_id=m.project_id AND t.milestone=m.id AND t.status=5) as completed_tasks',
    '(SELECT COUNT(t.id) FROM ' . db_prefix() . 'tasks t WHERE t.rel_type="project" AND t.rel_id=m.project_id AND t.milestone=m.id) as total_tasks',
    'm.is_billable',
    'm.billed',
    'm.id',
    'm.project_id'
];

$sIndexColumn = 'm.id';
$sTable       = db_prefix() . 'milestones m';

$join = [
    'LEFT JOIN ' . db_prefix() . 'projects p ON p.id = m.project_id'
];

$where = [];

// Apply filters
if ($this->input->post('filter_project') && $this->input->post('filter_project') != '') {
    array_push($where, 'AND m.project_id = ' . $this->db->escape_str($this->input->post('filter_project')));
}

if ($this->input->post('filter_status') && $this->input->post('filter_status') != '') {
    $status = $this->input->post('filter_status');
    switch ($status) {
        case 'billable':
            array_push($where, 'AND m.is_billable = 1 AND m.billed = 0');
            break;
        case 'completed':
            array_push($where, 'AND m.is_billable = 1 AND m.billed = 0 AND (SELECT COUNT(t.id) FROM ' . db_prefix() . 'tasks t WHERE t.rel_type="project" AND t.rel_id=m.project_id AND t.milestone=m.id) > 0 AND (SELECT COUNT(t.id) FROM ' . db_prefix() . 'tasks t WHERE t.rel_type="project" AND t.rel_id=m.project_id AND t.milestone=m.id AND t.status=5) = (SELECT COUNT(t.id) FROM ' . db_prefix() . 'tasks t WHERE t.rel_type="project" AND t.rel_id=m.project_id AND t.milestone=m.id)');
            break;
        case 'billed':
            array_push($where, 'AND m.billed = 1');
            break;
    }
}

if ($this->input->post('filter_billing_type') && $this->input->post('filter_billing_type') != '') {
    array_push($where, 'AND m.billing_type = ' . $this->db->escape_str($this->input->post('filter_billing_type')));
}

// Only show billable milestones
array_push($where, 'AND m.is_billable = 1');

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    'm.description',
    'm.start_date',
    'p.project_cost',
    'p.billing_type as project_billing_type'
]);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];
    
    // Project Name
    $row[] = '<a href="' . admin_url('projects/view/' . $aRow['project_id']) . '">' . e($aRow['project_name']) . '</a>';
    
    // Milestone Name
    $milestone_name = e($aRow['milestone_name']);
    if (strlen($milestone_name) > 50) {
        $milestone_name = substr($milestone_name, 0, 50) . '...';
    }
    $row[] = $milestone_name;
    
    // Due Date
    $row[] = $aRow['due_date'] ? _d($aRow['due_date']) : '-';
    
    // Billing Type
    $billing_type = '';
    if ($aRow['billing_type']) {
        $type = get_milestone_billing_type($aRow['billing_type']);
        $billing_type = $type ? get_milestone_billing_type_icon($aRow['billing_type']) . ' ' . $type['name'] : '-';
    }
    $row[] = $billing_type;
    
    // Rate
    $rate_display = '-';
    if ($aRow['rate'] && $aRow['billing_type']) {
        switch ($aRow['billing_type']) {
            case 1: // Fixed Rate
                $rate_display = format_milestone_billing_amount($aRow['rate']);
                break;
            case 2: // Percentage
                $rate_display = $aRow['rate'] . '%';
                break;
            case 3: // Hourly
                $rate_display = format_milestone_billing_amount($aRow['rate']) . '/hr';
                break;
        }
    }
    $row[] = $rate_display;
    
    // Completion
    $completion_percentage = $aRow['total_tasks'] > 0 ? round(($aRow['completed_tasks'] / $aRow['total_tasks']) * 100, 2) : 0;
    $completion_class = 'progress-bar-info';
    
    if ($completion_percentage == 100) {
        $completion_class = 'progress-bar-success';
    } elseif ($completion_percentage >= 75) {
        $completion_class = 'progress-bar-warning';
    } elseif ($completion_percentage < 25) {
        $completion_class = 'progress-bar-danger';
    }
    
    $completion_html = '<div class="progress progress-sm">
                            <div class="progress-bar ' . $completion_class . '" role="progressbar" 
                                 style="width: ' . $completion_percentage . '%;" 
                                 aria-valuenow="' . $completion_percentage . '" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                                ' . $completion_percentage . '%
                            </div>
                        </div>
                        <small class="text-muted">' . $aRow['completed_tasks'] . '/' . $aRow['total_tasks'] . ' tasks</small>';
    
    $row[] = $completion_html;
    
    // Calculated Amount
    $calculated_amount = 0;
    if ($aRow['rate'] && $aRow['billing_type']) {
        // Load milestone billing model to calculate amount
        $CI = &get_instance();
        if (!$CI->load->is_loaded('milestone_billing_model')) {
            $CI->load->model('milestone_billing/milestone_billing_model');
        }
        
        $milestone_data = [
            'id' => $aRow['id'],
            'rate' => $aRow['rate'],
            'billing_type' => $aRow['billing_type'],
            'is_billable' => $aRow['is_billable']
        ];
        
        $calculated_amount = $CI->milestone_billing_model->calculate_milestone_amount($milestone_data, $aRow['project_id']);
    }
    
    $amount_display = $calculated_amount > 0 ? '<strong class="text-success">' . format_milestone_billing_amount($calculated_amount) . '</strong>' : '-';
    $row[] = $amount_display;
    
    // Status
    $status_badge = '';
    if ($aRow['billed']) {
        $status_badge = '<span class="label label-success">' . _l('milestone_billing_progress_billed') . '</span>';
    } elseif ($completion_percentage == 100) {
        $status_badge = '<span class="label label-info">' . _l('milestone_billing_progress_completed') . '</span>';
    } elseif ($aRow['is_billable']) {
        $status_badge = '<span class="label label-warning">' . _l('milestone_billing_progress_in_progress') . '</span>';
    } else {
        $status_badge = '<span class="label label-default">' . _l('milestone_billing_progress_not_started') . '</span>';
    }
    $row[] = $status_badge;
    
    // Actions
    $actions = [];
    
    if (staff_can('view', 'milestone_billing')) {
        $actions[] = '<a href="' . admin_url('projects/view/' . $aRow['project_id'] . '?group=milestone_billing') . '" 
                         class="btn btn-default btn-xs" title="' . _l('view') . '">
                         <i class="fa fa-eye"></i>
                      </a>';
    }
    
    if (staff_can('edit', 'milestone_billing')) {
        $actions[] = '<a href="#" onclick="editMilestoneBilling(' . $aRow['id'] . ')" 
                         class="btn btn-default btn-xs" title="' . _l('edit') . '">
                         <i class="fa fa-edit"></i>
                      </a>';
        
        if (!$aRow['billed'] && $completion_percentage == 100) {
            $actions[] = '<a href="#" onclick="createInvoiceFromMilestone(' . $aRow['id'] . ')" 
                             class="btn btn-success btn-xs" title="' . _l('milestone_billing_create_invoice') . '">
                             <i class="fa fa-file-invoice"></i>
                          </a>';
        }
    }
    
    $row[] = implode(' ', $actions);
    
    $output['aaData'][] = $row;
}

echo json_encode($output);
