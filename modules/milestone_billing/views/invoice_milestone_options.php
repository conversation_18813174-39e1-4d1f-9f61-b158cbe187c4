<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<?php if (!empty($billable_milestones)) { ?>
<hr>
<div class="milestone-billing-invoice-section">
    <h5><i class="fa fa-money-bill"></i> <?= _l('milestone_billing'); ?></h5>
    
    <div class="row">
        <div class="col-md-12">
            <div class="radio radio-primary">
                <input type="radio" name="invoice_data_type" value="milestone_billing" id="milestone_billing">
                <label for="milestone_billing"><?= _l('invoice_project_data_milestone_billing'); ?></label>
            </div>
            <div class="col-md-2 mtop10 text-right">
                <a href="#" class="text-muted" data-toggle="popover" data-placement="bottom"
                    data-content="<b><?= _l('invoice_project_item_name_data'); ?>:</b> <?= _l('milestone_billing_invoice_item_name'); ?><br /><b><?= _l('invoice_project_description_data'); ?>:</b> <?= _l('milestone_billing_invoice_item_description'); ?>"
                    data-html="true"><i class="fa-regular fa-circle-question"></i></a>
            </div>
        </div>
    </div>
    
    <div id="milestone_billing_options" style="display: none; margin-top: 15px;">
        <div class="alert alert-info">
            <i class="fa fa-info-circle"></i>
            <?= _l('milestone_billing_select_milestones_for_invoice'); ?>
        </div>
        
        <div class="checkbox checkbox-primary">
            <input type="checkbox" id="select_all_billable_milestones">
            <label for="select_all_billable_milestones"><?= _l('milestone_billing_select_all_completed'); ?></label>
        </div>
        
        <hr>
        
        <div id="billable_milestones_list">
            <?php foreach ($billable_milestones as $milestone) { ?>
            <div class="checkbox checkbox-default mbot15">
                <input type="checkbox" name="milestone_ids[]" 
                       value="<?= e($milestone['id']); ?>"
                       <?php if ($milestone['is_completed']) { echo 'checked '; } ?>
                       id="milestone_<?= e($milestone['id']); ?>"
                       data-amount="<?= $milestone['calculated_amount']; ?>">
                <label class="inline-block full-width" for="milestone_<?= e($milestone['id']); ?>">
                    <strong><?= e($milestone['name']); ?></strong>
                    <?php if ($milestone['calculated_amount'] > 0) { ?>
                    <span class="pull-right text-success">
                        <strong><?= format_milestone_billing_amount($milestone['calculated_amount']); ?></strong>
                    </span>
                    <?php } ?>
                    <br>
                    <small class="text-muted">
                        <?= get_milestone_billing_type($milestone['billing_type'])['name'] ?? ''; ?>
                        <?php if ($milestone['due_date']) { ?>
                        | <?= _l('due_date'); ?>: <?= _d($milestone['due_date']); ?>
                        <?php } ?>
                        <?php if (!$milestone['is_completed']) { ?>
                        | <span class="text-warning"><?= _l('milestone_not_completed'); ?></span>
                        <?php } ?>
                    </small>
                </label>
            </div>
            <?php } ?>
        </div>
        
        <div class="milestone-billing-summary" style="margin-top: 15px; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">
            <div class="row">
                <div class="col-md-6">
                    <strong><?= _l('milestone_billing_selected_count'); ?>:</strong>
                    <span id="selected_milestones_count">0</span>
                </div>
                <div class="col-md-6 text-right">
                    <strong><?= _l('milestone_billing_total_amount'); ?>:</strong>
                    <span id="selected_milestones_total" class="text-success">
                        <?= format_milestone_billing_amount(0); ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Handle milestone billing radio button
    $('input[name="invoice_data_type"]').change(function() {
        if ($(this).val() === 'milestone_billing' && $(this).is(':checked')) {
            $('#milestone_billing_options').slideDown();
            updateMilestoneBillingSummary();
        } else {
            $('#milestone_billing_options').slideUp();
        }
    });
    
    // Handle select all milestones
    $('#select_all_billable_milestones').change(function() {
        var isChecked = $(this).is(':checked');
        $('input[name="milestone_ids[]"]').prop('checked', isChecked);
        updateMilestoneBillingSummary();
    });
    
    // Handle individual milestone selection
    $(document).on('change', 'input[name="milestone_ids[]"]', function() {
        updateMilestoneBillingSummary();
        updateSelectAllMilestonesCheckbox();
    });
    
    // Initialize summary
    updateMilestoneBillingSummary();
});

function updateMilestoneBillingSummary() {
    var selectedMilestones = $('input[name="milestone_ids[]"]:checked');
    var count = selectedMilestones.length;
    var total = 0;
    
    selectedMilestones.each(function() {
        var amount = parseFloat($(this).data('amount')) || 0;
        total += amount;
    });
    
    $('#selected_milestones_count').text(count);
    $('#selected_milestones_total').text(app_format_money(total));
}

function updateSelectAllMilestonesCheckbox() {
    var totalCheckboxes = $('input[name="milestone_ids[]"]').length;
    var checkedCheckboxes = $('input[name="milestone_ids[]"]:checked').length;
    
    $('#select_all_billable_milestones').prop('checked', totalCheckboxes > 0 && checkedCheckboxes === totalCheckboxes);
}

// Override the invoice_project function to handle milestone billing
if (typeof original_invoice_project === 'undefined') {
    var original_invoice_project = window.invoice_project;
}

window.invoice_project = function(project_id) {
    var invoiceType = $('input[name="invoice_data_type"]:checked').val();
    
    if (invoiceType === 'milestone_billing') {
        var selectedMilestones = $('input[name="milestone_ids[]"]:checked');
        
        if (selectedMilestones.length === 0) {
            alert('<?= _l('milestone_billing_no_milestones_selected'); ?>');
            return;
        }
        
        // Create form data for milestone billing
        var data = {
            project_id: project_id,
            invoice_type: 'milestone_billing',
            milestones: []
        };
        
        selectedMilestones.each(function() {
            data.milestones.push($(this).val());
        });
        
        // Submit to milestone billing controller
        $.post(admin_url + 'milestone_billing/create_milestone_invoice/' + project_id, data)
            .done(function(response) {
                $("#pre_invoice_project_settings").modal("hide");
                location.reload();
            })
            .fail(function() {
                alert('<?= _l('milestone_billing_invoice_creation_failed'); ?>');
            });
    } else {
        // Call original function for other invoice types
        if (typeof original_invoice_project === 'function') {
            original_invoice_project(project_id);
        }
    }
};
</script>

<style>
.milestone-billing-invoice-section {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.milestone-billing-invoice-section h5 {
    margin-bottom: 15px;
    color: #333;
}

#billable_milestones_list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background-color: #fff;
    border-radius: 4px;
}

.milestone-billing-summary {
    border: 1px solid #ddd;
}
</style>

<?php } ?>

<?php
// Add language strings for JavaScript
if (!isset($milestone_billing_lang_added)) {
    $milestone_billing_lang_added = true;
?>
<script>
// Add language strings for milestone billing
var milestone_billing_lang = {
    'no_milestones_selected': '<?= _l('milestone_billing_no_milestones_selected'); ?>',
    'invoice_creation_failed': '<?= _l('milestone_billing_invoice_creation_failed'); ?>',
    'select_milestones_for_invoice': '<?= _l('milestone_billing_select_milestones_for_invoice'); ?>'
};
</script>
<?php } ?>
