<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Milestone_billing_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get milestone with rate information
     */
    public function get_milestone_rate($milestone_id)
    {
        try {
            $this->db->select('id, name, description, rate, due_date');
            $this->db->where('id', $milestone_id);
            $result = $this->db->get(db_prefix() . 'milestones');

            if ($result) {
                return $result->row_array();
            }
            return null;
        } catch (Exception $e) {
            log_message('error', 'Milestone Billing Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get project milestones with rates
     */
    public function get_project_milestones_with_rates($project_id)
    {
        try {
            $this->db->select('id, name, description, rate, due_date');
            $this->db->where('project_id', $project_id);
            $this->db->where('rate IS NOT NULL');
            $this->db->where('rate >', 0);
            $this->db->order_by('milestone_order', 'ASC');
            $result = $this->db->get(db_prefix() . 'milestones');

            if ($result) {
                return $result->result_array();
            }
            return [];
        } catch (Exception $e) {
            log_message('error', 'Milestone Billing Error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Update milestone rate
     */
    public function update_milestone_rate($milestone_id, $rate)
    {
        $this->db->where('id', $milestone_id);
        return $this->db->update(db_prefix() . 'milestones', ['rate' => $rate]);
    }
}
