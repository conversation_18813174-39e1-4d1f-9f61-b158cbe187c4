<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Milestone_billing extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('milestone_billing_model');
        $this->load->model('projects_model');
        $this->load->model('invoices_model');
    }

    /**
     * Get milestone rate for editing
     */
    public function get_milestone_rate($milestone_id)
    {
        $milestone = $this->milestone_billing_model->get_milestone_rate($milestone_id);
        echo json_encode(['rate' => $milestone ? $milestone['rate'] : null]);
    }

    /**
     * Save milestone rate for existing milestone
     */
    public function save_milestone_rate()
    {
        $milestone_id = $this->input->post('milestone_id');
        $rate = $this->input->post('rate');

        if ($milestone_id && $rate !== null) {
            $success = $this->milestone_billing_model->update_milestone_rate($milestone_id, $rate);
            echo json_encode(['success' => $success]);
        } else {
            echo json_encode(['success' => false]);
        }
    }

    /**
     * Save rate for the latest milestone (for new milestones)
     */
    public function save_rate_for_latest_milestone()
    {
        $rate = $this->input->post('rate');

        if ($rate !== null) {
            // Get the latest milestone for the current user/session
            $this->db->select('id');
            $this->db->order_by('id', 'DESC');
            $this->db->limit(1);
            $result = $this->db->get(db_prefix() . 'milestones')->row();

            if ($result) {
                $success = $this->milestone_billing_model->update_milestone_rate($result->id, $rate);
                echo json_encode(['success' => $success, 'milestone_id' => $result->id]);
            } else {
                echo json_encode(['success' => false]);
            }
        } else {
            echo json_encode(['success' => false]);
        }
    }

    /**
     * Create invoice from milestones
     */
    public function create_milestone_invoice($project_id)
    {
        if (!staff_can('create', 'invoices')) {
            access_denied('invoices');
        }

        $milestone_ids = $this->input->post('milestone_ids');

        if (empty($milestone_ids)) {
            set_alert('warning', 'No milestones selected');
            redirect(admin_url('projects/view/' . $project_id));
        }

        $project = $this->projects_model->get($project_id);
        $invoice_data = [
            'clientid' => $project->clientid,
            'project_id' => $project_id,
            'date' => date('Y-m-d'),
            'duedate' => date('Y-m-d', strtotime('+30 days')),
            'currency' => get_base_currency()->id,
            'newitems' => []
        ];

        foreach ($milestone_ids as $milestone_id) {
            $milestone = $this->milestone_billing_model->get_milestone_rate($milestone_id);

            if ($milestone && $milestone['rate'] > 0) {
                $invoice_data['newitems'][] = [
                    'description' => 'Milestone: ' . $milestone['name'],
                    'long_description' => $milestone['description'],
                    'qty' => 1,
                    'rate' => $milestone['rate'],
                    'taxname_1' => '',
                    'taxrate_1' => 0,
                    'taxname_2' => '',
                    'taxrate_2' => 0,
                    'unit' => ''
                ];
            }
        }

        if (!empty($invoice_data['newitems'])) {
            $invoice_id = $this->invoices_model->add($invoice_data);

            if ($invoice_id) {
                set_alert('success', 'Invoice created successfully from milestones');
                redirect(admin_url('invoices/list_invoices/' . $invoice_id));
            }
        }

        set_alert('danger', 'Failed to create invoice from milestones');
        redirect(admin_url('projects/view/' . $project_id));
    }

}
