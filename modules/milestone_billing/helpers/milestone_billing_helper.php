<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Format milestone billing amount for display
 * @param float $amount
 * @param int $currency_id
 * @return string
 */
function format_milestone_billing_amount($amount, $currency_id = null)
{
    if ($currency_id === null) {
        $currency_id = get_base_currency()->id;
    }
    
    return app_format_money($amount, get_currency($currency_id));
}

/**
 * Get milestone billing status badge
 * @param array $milestone
 * @return string
 */
function get_milestone_billing_status_badge($milestone)
{
    if (!$milestone['is_billable']) {
        return '<span class="label label-default">' . _l('milestone_billing_progress_not_started') . '</span>';
    }
    
    if ($milestone['billed']) {
        return '<span class="label label-success">' . _l('milestone_billing_progress_billed') . '</span>';
    }
    
    if ($milestone['is_completed']) {
        return '<span class="label label-info">' . _l('milestone_billing_progress_completed') . '</span>';
    }
    
    return '<span class="label label-warning">' . _l('milestone_billing_progress_in_progress') . '</span>';
}

/**
 * Get milestone completion percentage
 * @param int $milestone_id
 * @return float
 */
function get_milestone_completion_percentage($milestone_id)
{
    $CI = &get_instance();
    
    $CI->db->select('COUNT(*) as total_tasks');
    $CI->db->from(db_prefix() . 'tasks');
    $CI->db->where('milestone', $milestone_id);
    $total = $CI->db->get()->row_array()['total_tasks'];
    
    if ($total == 0) {
        return 0;
    }
    
    $CI->db->select('COUNT(*) as completed_tasks');
    $CI->db->from(db_prefix() . 'tasks');
    $CI->db->where('milestone', $milestone_id);
    $CI->db->where('status', 5); // Status 5 = completed
    $completed = $CI->db->get()->row_array()['completed_tasks'];
    
    return round(($completed / $total) * 100, 2);
}

/**
 * Check if milestone can be billed
 * @param array $milestone
 * @return bool
 */
function can_milestone_be_billed($milestone)
{
    if (!$milestone['is_billable'] || $milestone['billed']) {
        return false;
    }
    
    // Check if completion is required
    $CI = &get_instance();
    if (!$CI->load->is_loaded('milestone_billing_model')) {
        $CI->load->model('milestone_billing/milestone_billing_model');
    }
    
    $settings = $CI->milestone_billing_model->get_project_billing_settings($milestone['project_id']);
    
    if ($settings && $settings['require_milestone_completion']) {
        return $milestone['is_completed'];
    }
    
    return true;
}

/**
 * Get milestone billing type icon
 * @param int $billing_type
 * @return string
 */
function get_milestone_billing_type_icon($billing_type)
{
    switch ($billing_type) {
        case 1: // Fixed Rate
            return '<i class="fa fa-dollar-sign" title="' . _l('milestone_billing_fixed_rate') . '"></i>';
        case 2: // Percentage
            return '<i class="fa fa-percentage" title="' . _l('milestone_billing_percentage') . '"></i>';
        case 3: // Hourly
            return '<i class="fa fa-clock" title="' . _l('milestone_billing_hourly') . '"></i>';
        default:
            return '<i class="fa fa-question" title="Unknown"></i>';
    }
}

/**
 * Calculate milestone progress bar HTML
 * @param array $milestone
 * @return string
 */
function milestone_progress_bar($milestone)
{
    $percentage = $milestone['completion_percentage'] ?? 0;
    $class = 'progress-bar-info';
    
    if ($percentage == 100) {
        $class = 'progress-bar-success';
    } elseif ($percentage >= 75) {
        $class = 'progress-bar-warning';
    } elseif ($percentage < 25) {
        $class = 'progress-bar-danger';
    }
    
    return '<div class="progress progress-sm">
                <div class="progress-bar ' . $class . '" role="progressbar" 
                     style="width: ' . $percentage . '%;" 
                     aria-valuenow="' . $percentage . '" 
                     aria-valuemin="0" 
                     aria-valuemax="100">
                    ' . $percentage . '%
                </div>
            </div>';
}

/**
 * Get milestone billing actions HTML
 * @param array $milestone
 * @return string
 */
function get_milestone_billing_actions($milestone)
{
    $actions = [];
    
    if (staff_can('edit', 'milestone_billing')) {
        $actions[] = '<a href="#" onclick="edit_milestone_billing(' . $milestone['id'] . ')" 
                         class="btn btn-default btn-xs" title="' . _l('milestone_billing_edit_billing') . '">
                         <i class="fa fa-edit"></i>
                      </a>';
        
        if (can_milestone_be_billed($milestone)) {
            $actions[] = '<a href="#" onclick="create_milestone_invoice(' . $milestone['id'] . ')" 
                             class="btn btn-success btn-xs" title="' . _l('milestone_billing_create_invoice') . '">
                             <i class="fa fa-file-invoice"></i>
                          </a>';
        }
        
        if ($milestone['billed']) {
            $actions[] = '<a href="#" onclick="unmark_milestone_billed(' . $milestone['id'] . ')" 
                             class="btn btn-warning btn-xs" title="' . _l('milestone_billing_unmark_billed') . '">
                             <i class="fa fa-undo"></i>
                          </a>';
        }
    }
    
    return implode(' ', $actions);
}

/**
 * Validate milestone billing data
 * @param array $data
 * @return array
 */
function validate_milestone_billing_data($data)
{
    $errors = [];
    
    if (isset($data['is_billable']) && $data['is_billable']) {
        if (empty($data['billing_type'])) {
            $errors[] = _l('milestone_billing_type_required');
        }
        
        if (empty($data['rate']) || !is_numeric($data['rate']) || $data['rate'] <= 0) {
            $errors[] = _l('milestone_billing_rate_required');
        }
        
        if ($data['billing_type'] == 2 && ($data['rate'] < 0 || $data['rate'] > 100)) {
            $errors[] = _l('milestone_billing_invalid_percentage');
        }
    }
    
    return $errors;
}

/**
 * Get project milestone billing summary widget
 * @param int $project_id
 * @return string
 */
function get_project_milestone_billing_widget($project_id)
{
    $CI = &get_instance();
    
    if (!$CI->load->is_loaded('milestone_billing_model')) {
        $CI->load->model('milestone_billing/milestone_billing_model');
    }
    
    $summary = $CI->milestone_billing_model->get_project_billing_summary($project_id);
    
    $html = '<div class="widget" id="milestone-billing-widget">
                <div class="widget-header">
                    <h3>' . _l('milestone_billing_summary') . '</h3>
                </div>
                <div class="widget-content">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">' . _l('milestone_billing_total_milestones') . ':</span>
                                <span class="info-value">' . $summary['total_milestones'] . '</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">' . _l('milestone_billing_billable_milestones') . ':</span>
                                <span class="info-value">' . $summary['billable_milestones'] . '</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">' . _l('milestone_billing_billed_milestones') . ':</span>
                                <span class="info-value">' . $summary['billed_milestones'] . '</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <span class="info-label">' . _l('milestone_billing_total_billable_amount') . ':</span>
                                <span class="info-value">' . format_milestone_billing_amount($summary['total_billable_amount']) . '</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">' . _l('milestone_billing_total_billed_amount') . ':</span>
                                <span class="info-value text-success">' . format_milestone_billing_amount($summary['total_billed_amount']) . '</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">' . _l('milestone_billing_pending_amount') . ':</span>
                                <span class="info-value text-warning">' . format_milestone_billing_amount($summary['pending_amount']) . '</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
    
    return $html;
}

/**
 * Check if project has milestone billing enabled
 * @param int $project_id
 * @return bool
 */
function project_has_milestone_billing($project_id)
{
    $CI = &get_instance();
    
    $CI->db->select('COUNT(*) as count');
    $CI->db->from(db_prefix() . 'milestones');
    $CI->db->where('project_id', $project_id);
    $CI->db->where('is_billable', 1);
    
    $result = $CI->db->get()->row_array();
    return $result['count'] > 0;
}

/**
 * Get milestone billing rate display
 * @param array $milestone
 * @return string
 */
function get_milestone_rate_display($milestone)
{
    if (!$milestone['is_billable'] || !$milestone['rate']) {
        return '-';
    }
    
    switch ($milestone['billing_type']) {
        case 1: // Fixed Rate
            return format_milestone_billing_amount($milestone['rate']);
        case 2: // Percentage
            return $milestone['rate'] . '%';
        case 3: // Hourly
            return format_milestone_billing_amount($milestone['rate']) . '/hr';
        default:
            return format_milestone_billing_amount($milestone['rate']);
    }
}
