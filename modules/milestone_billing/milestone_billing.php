<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Milestone Billing
Description: Add rate field to milestones and invoice based on milestones
Version: 1.0.0
Requires at least: 2.3.*
*/

define('MILESTONE_BILLING_MODULE_NAME', 'milestone_billing');

/**
 * Register activation module hook
 */
register_activation_hook(MILESTONE_BILLING_MODULE_NAME, 'milestone_billing_activation_hook');

function milestone_billing_activation_hook()
{
    require_once __DIR__ . '/install.php';
}

/**
 * Register language files
 */
register_language_files(MILESTONE_BILLING_MODULE_NAME, [MILESTONE_BILLING_MODULE_NAME]);

// Only add hooks if we're not in the activation process
if (!defined('MODULE_ACTIVATION_IN_PROGRESS')) {
    hooks()->add_action('modules_loaded', 'milestone_billing_init_hooks');
}

function milestone_billing_init_hooks()
{
    // Add JavaScript to inject rate field into milestone form
    hooks()->add_action('app_admin_head', 'milestone_billing_add_scripts');

    // Add milestone options to invoice generation
    hooks()->add_action('before_project_invoice_form', 'milestone_billing_add_invoice_options');

    // Hook into admin_init to override milestone handling
    hooks()->add_action('admin_init', 'milestone_billing_override_milestone_handling');
}

/**
 * Override milestone handling to save rate data
 */
function milestone_billing_override_milestone_handling()
{
    // Remove the complex override logic that might be causing 500 errors
    // We'll handle this differently using AJAX
}

/**
 * Add scripts to admin head for milestone form enhancement
 */
function milestone_billing_add_scripts()
{
    $CI = &get_instance();

    // Only add on project pages
    if ($CI->router->fetch_class() == 'projects') {
        echo '<script type="text/javascript">
        try {
            console.log("Milestone Billing: Script loaded");

            // Wait for jQuery to be available
            function waitForJQuery(callback) {
                if (typeof jQuery !== "undefined") {
                    callback(jQuery);
                } else {
                    setTimeout(function() {
                        waitForJQuery(callback);
                    }, 100);
                }
            }

            waitForJQuery(function($) {
                try {
                    console.log("Milestone Billing: jQuery ready");

                    $(document).ready(function() {
                        try {
                            console.log("Milestone Billing: Document ready");

                            // Function to add rate field to milestone form
                            function addMilestoneRateField() {
                                try {
                                    if ($("#milestone").length > 0 && $("#milestone_rate").length === 0) {
                                        var rateField = \'<div class="form-group">\' +
                                            \'<label for="milestone_rate" class="control-label">Rate</label>\' +
                                            \'<input type="number" id="milestone_rate" class="form-control" step="0.01" min="0" placeholder="Enter milestone rate">\' +
                                            \'<small class="text-muted">Optional: Set a rate for this milestone to include in invoicing</small>\' +
                                            \'</div>\';
                                        $("#milestone .modal-body").append(rateField);
                                        console.log("Milestone Billing: Rate field added");
                                    }
                                } catch(e) {
                                    console.log("Error adding rate field:", e);
                                }
                            }

                            // Add rate field when milestone modal is shown
                            $(document).on("shown.bs.modal", "#milestone", function() {
                                setTimeout(function() {
                                    addMilestoneRateField();

                                    var milestoneId = $("#milestone input[name=\'id\']").val();
                                    if (milestoneId && milestoneId !== "" && typeof admin_url !== "undefined") {
                                        // Load existing rate
                                        $.get(admin_url + "milestone_billing/get_milestone_rate/" + milestoneId)
                                            .done(function(response) {
                                                try {
                                                    var data = JSON.parse(response);
                                                    if (data.rate) {
                                                        $("#milestone_rate").val(data.rate);
                                                    }
                                                } catch(e) {
                                                    console.log("Error parsing milestone rate response");
                                                }
                                            })
                                            .fail(function() {
                                                console.log("Failed to load milestone rate");
                                            });
                                    }
                                }, 100);
                            });

                            // Also add field when milestone modal content is loaded
                            $(document).on("click", "[data-toggle=\'modal\'][data-target=\'#milestone\']", function() {
                                setTimeout(function() {
                                    addMilestoneRateField();
                                }, 200);
                            });

                        } catch(e) {
                            console.log("Error in document ready:", e);
                        }
                    });
                } catch(e) {
                    console.log("Error in jQuery callback:", e);
                }
            });
        } catch(e) {
            console.log("Error in milestone billing script:", e);
        }
        </script>';
    }
}

/**
 * Add milestone options to project invoice form
 */
function milestone_billing_add_invoice_options($project_id)
{
    try {
        $CI = &get_instance();

        if (!$CI->load->is_loaded('milestone_billing_model')) {
            $CI->load->model('milestone_billing/milestone_billing_model');
        }

        $milestones = $CI->milestone_billing_model->get_project_milestones_with_rates($project_id);

        if (!empty($milestones)) {
            echo '<hr>
            <div class="milestone-billing-section">
                <div class="row">
                    <div class="col-md-10">
                        <div class="radio radio-primary">
                            <input type="radio" name="invoice_data_type" value="milestones" id="invoice_milestones">
                            <label for="invoice_milestones">Invoice based on milestones</label>
                        </div>
                    </div>
                </div>

                <div id="milestone_invoice_options" style="display: none; margin-top: 15px;">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        Select milestones to include in the invoice
                    </div>';

            foreach ($milestones as $milestone) {
                if ($milestone['rate'] > 0) {
                    echo '<div class="checkbox checkbox-default">
                        <input type="checkbox" name="milestone_ids[]" value="' . $milestone['id'] . '" id="milestone_' . $milestone['id'] . '">
                        <label for="milestone_' . $milestone['id'] . '">
                            <strong>' . e($milestone['name']) . '</strong> - ' . app_format_money($milestone['rate']) . '
                            <br><small class="text-muted">Due: ' . ($milestone['due_date'] ? _d($milestone['due_date']) : 'No due date') . '</small>
                        </label>
                    </div>';
                }
            }

            echo '</div>
            </div>';
        }
    } catch (Exception $e) {
        // Silently fail to prevent 500 errors
        error_log('Milestone Billing Error: ' . $e->getMessage());
    }
}


