# Debug Steps for Change Request Issues

## Current Status
- ✅ Change Request creation is working (getting success response with ID 4)
- ❌ Redirect is not working properly
- ❌ Change Requests not showing in the list

## Step 1: Test the Redirect

1. **Open Browser Console**:
   - Press F12 to open Developer Tools
   - Go to Console tab

2. **Create a Change Request**:
   - Fill out the form and submit
   - Watch the console for these messages:
     - "Form response: {success: true, message: "...", id: 4, redirect: "..."}"
     - "Redirecting to: http://localhost:8000/admin/project_cr/change_request/4"

3. **Expected Behavior**:
   - Should show success message
   - Should redirect after 1 second to the change request details page

## Step 2: Test the Data Table

1. **Check Console for Table Loading**:
   - Look for: "Loading table from: http://localhost:8000/admin/project_cr/project_table/PROJECT_ID"
   - Look for any DataTable errors

2. **Check Network Tab**:
   - Go to Network tab in Developer Tools
   - Reload the Change Requests tab
   - Look for the AJAX request to `project_cr/project_table/PROJECT_ID`
   - Click on it to see the response

3. **Expected Response**:
   ```json
   {
     "draw": 1,
     "recordsTotal": 1,
     "recordsFiltered": 1,
     "data": [
       ["Change Request Title", "Medium", "Draft", "Staff Name", "2024-01-01 12:00:00", "Actions"]
     ]
   }
   ```

## Step 3: Manual Data Check

1. **Visit Debug URL**:
   - Go to: `http://localhost:8000/admin/project_cr/debug_crs/PROJECT_ID`
   - Replace PROJECT_ID with your actual project ID (probably 1)
   - This will show you if the data exists in the database

2. **Expected Output**:
   ```
   Debug Change Requests for Project ID: 1
   Found X change requests
   [Table showing your change requests]
   Testing Table Query
   Direct query found: X records
   ```

## Step 4: Check Database Directly

If you have database access, run this query:

```sql
SELECT * FROM tblproject_change_requests WHERE project_id = 1;
```

Expected result: Should show your created change requests.

## Step 5: Common Issues and Solutions

### Issue 1: "admin_url is not defined" in console

**Solution**: The JavaScript has been updated to use PHP-generated URLs instead of the `admin_url` variable.

### Issue 2: DataTable shows "No data available"

**Possible Causes**:
1. **Permissions**: Check if your user role has "View" permission for Change Requests
2. **Project ID**: Make sure you're in the correct project context
3. **Data**: Use the debug URL to verify data exists

### Issue 3: Redirect not working

**Possible Causes**:
1. **JavaScript errors**: Check browser console for errors
2. **URL format**: The redirect URL should be properly formatted
3. **Timing**: Added 1-second delay to show success message first

### Issue 4: Table AJAX errors

**Check**:
1. **URL**: Should be `http://localhost:8000/admin/project_cr/project_table/PROJECT_ID`
2. **Response**: Should return JSON with DataTable format
3. **Permissions**: User must have view permissions

## Step 6: Quick Fixes

### Fix 1: Force Table Reload
Add this to browser console after creating a CR:
```javascript
$('.table-project-change-requests').DataTable().ajax.reload();
```

### Fix 2: Manual Redirect
If redirect doesn't work, manually go to:
```
http://localhost:8000/admin/project_cr/change_request/4
```
(Replace 4 with your actual CR ID)

### Fix 3: Check Permissions
1. Go to Setup → Staff → Roles
2. Edit your role
3. Under "Project Change Requests":
   - ✅ View
   - ✅ Create
   - ✅ Edit

## Step 7: Test Results

After following these steps, you should be able to identify:

1. **Is the data being created?** (Check debug URL or database)
2. **Is the table loading correctly?** (Check console and network tab)
3. **Are there permission issues?** (Check role permissions)
4. **Are there JavaScript errors?** (Check browser console)

## Next Steps

Based on what you find:

1. **If data exists but table is empty**: Permission or query issue
2. **If no data in database**: Creation issue (but this seems to work)
3. **If JavaScript errors**: Need to fix specific errors
4. **If redirect not working**: Browser console will show the issue

Let me know what you see in the browser console and debug URL, and I can provide more specific fixes!
