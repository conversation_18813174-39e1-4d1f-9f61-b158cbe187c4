<?php

defined('BASEPATH') or exit('No direct script access allowed');

// Module
$lang['project_change_requests'] = 'Change Requests';
$lang['change_requests'] = 'Change Requests';
$lang['change_request'] = 'Change Request';
$lang['change_request_lowercase'] = 'change request';
$lang['new_change_request'] = 'New Change Request';
$lang['add_change_request'] = 'Add Change Request';
$lang['edit_change_request'] = 'Edit Change Request';
$lang['change_request_not_found'] = 'Change Request Not Found';

// Fields
$lang['cr_title'] = 'Title';
$lang['cr_description'] = 'Description';
$lang['cr_justification'] = 'Justification';
$lang['cr_impact_analysis'] = 'Impact Analysis';
$lang['cr_estimated_cost'] = 'Estimated Cost';
$lang['cr_estimated_hours'] = 'Estimated Hours';
$lang['cr_priority'] = 'Priority';
$lang['cr_status'] = 'Status';
$lang['cr_requested_by'] = 'Requested By';
$lang['cr_assigned_to'] = 'Assigned To';
$lang['cr_approved_by'] = 'Approved By';
$lang['cr_approved_date'] = 'Approved Date';
$lang['cr_implementation_date'] = 'Implementation Date';
$lang['cr_rejection_reason'] = 'Rejection Reason';
$lang['cr_date_created'] = 'Date Created';
$lang['cr_date_updated'] = 'Date Updated';

// Statuses
$lang['cr_status_draft'] = 'Draft';
$lang['cr_status_submitted'] = 'Submitted';
$lang['cr_status_approved'] = 'Approved';
$lang['cr_status_rejected'] = 'Rejected';
$lang['cr_status_implemented'] = 'Implemented';

// Priorities
$lang['cr_priority_low'] = 'Low';
$lang['cr_priority_medium'] = 'Medium';
$lang['cr_priority_high'] = 'High';
$lang['cr_priority_critical'] = 'Critical';

// Actions
$lang['submit_change_request'] = 'Submit Change Request';
$lang['approve_change_request'] = 'Approve Change Request';
$lang['reject_change_request'] = 'Reject Change Request';
$lang['implement_change_request'] = 'Mark as Implemented';
$lang['update_status'] = 'Update Status';

// Messages
$lang['change_request_submitted_successfully'] = 'Change request submitted successfully';
$lang['change_request_approved_successfully'] = 'Change request approved successfully';
$lang['change_request_rejected_successfully'] = 'Change request rejected successfully';
$lang['change_request_implemented_successfully'] = 'Change request marked as implemented';
$lang['comment_added_successfully'] = 'Comment added successfully';

// Comments
$lang['cr_comments'] = 'Comments';
$lang['add_comment'] = 'Add Comment';
$lang['no_comments_found'] = 'No comments found';

// Statistics
$lang['cr_statistics'] = 'Change Request Statistics';
$lang['total_change_requests'] = 'Total Change Requests';
$lang['draft_change_requests'] = 'Draft';
$lang['submitted_change_requests'] = 'Submitted';
$lang['approved_change_requests'] = 'Approved';
$lang['rejected_change_requests'] = 'Rejected';
$lang['implemented_change_requests'] = 'Implemented';

// Table headers
$lang['cr_table_title'] = 'Title';
$lang['cr_table_project'] = 'Project';
$lang['cr_table_priority'] = 'Priority';
$lang['cr_table_status'] = 'Status';
$lang['cr_table_requested_by'] = 'Requested By';
$lang['cr_table_date_created'] = 'Date Created';
$lang['cr_table_actions'] = 'Actions';

// Tooltips and help text
$lang['cr_title_help'] = 'Brief title describing the change request';
$lang['cr_description_help'] = 'Detailed description of the requested change';
$lang['cr_justification_help'] = 'Business justification for this change';
$lang['cr_impact_analysis_help'] = 'Analysis of the impact this change will have on the project';
$lang['cr_estimated_cost_help'] = 'Estimated additional cost for implementing this change';
$lang['cr_estimated_hours_help'] = 'Estimated additional hours required for implementation';

// Permissions
$lang['permission_view_change_requests'] = 'View Change Requests';
$lang['permission_create_change_requests'] = 'Create Change Requests';
$lang['permission_edit_change_requests'] = 'Edit Change Requests';
$lang['permission_delete_change_requests'] = 'Delete Change Requests';
