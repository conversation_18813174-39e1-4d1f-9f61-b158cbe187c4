# Quick Fix Guide - Project Change Requests Module

## Issues Fixed

### ✅ **1. Removed Sidebar Menu**
- Commented out the sidebar menu initialization
- Now only shows the tab in project details page

### ✅ **2. Fixed Project Tab Integration**
- Updated view path to use `module_views_path()` function
- Fixed project variable access with safety checks

### ✅ **3. Fixed Language Function Calls**
- Added `function_exists('_l')` checks before all language calls
- Added fallback text for when language functions aren't available

### ✅ **4. Fixed Staff Function Calls**
- Added `function_exists('staff_can')` checks
- Added safety checks for all Perfex CRM functions

### ✅ **5. Fixed Project Variable Access**
- Added `isset($project)` checks in views
- Added fallback values for project ID

## Testing Steps

1. **Activate the Module**:
   - Go to Setup → Modules
   - Find "Project Change Requests"
   - Click "Activate"

2. **Test Project Tab**:
   - Go to any project (Projects → View any project)
   - Look for "Change Requests" tab
   - Click on the tab to verify it loads

3. **Test Creating Change Request**:
   - In the Change Requests tab, click "New Change Request"
   - Fill in the form and submit
   - Verify it appears in the table

## If Still Having Issues

### Issue: Tab Not Showing
**Solution**: Clear any caches and refresh the page. Make sure the module is activated.

### Issue: 500 Error on Tab Click
**Check**: 
1. PHP error logs for specific error
2. Database permissions
3. File permissions on module directory

### Issue: Form Not Submitting
**Check**:
1. AJAX calls in browser console
2. Controller method `add_project_cr` exists
3. Database table was created properly

## Manual Database Check

If the module activated but tables weren't created, run this SQL:

```sql
-- Check if tables exist
SHOW TABLES LIKE '%project_change_requests%';

-- If not, create manually:
CREATE TABLE `tblproject_change_requests` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `project_id` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `description` text NOT NULL,
    `justification` text,
    `impact_analysis` text,
    `estimated_cost` decimal(15,2) DEFAULT NULL,
    `estimated_hours` decimal(8,2) DEFAULT NULL,
    `priority` tinyint(1) NOT NULL DEFAULT 2,
    `status` tinyint(1) NOT NULL DEFAULT 1,
    `requested_by` int(11) NOT NULL,
    `assigned_to` int(11) DEFAULT NULL,
    `approved_by` int(11) DEFAULT NULL,
    `approved_date` datetime DEFAULT NULL,
    `implementation_date` datetime DEFAULT NULL,
    `rejection_reason` text,
    `date_created` datetime NOT NULL,
    `date_updated` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `project_id` (`project_id`),
    KEY `requested_by` (`requested_by`),
    KEY `assigned_to` (`assigned_to`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tblproject_cr_comments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `cr_id` int(11) NOT NULL,
    `staff_id` int(11) NOT NULL,
    `comment` text NOT NULL,
    `date_created` datetime NOT NULL,
    PRIMARY KEY (`id`),
    KEY `cr_id` (`cr_id`),
    KEY `staff_id` (`staff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tblproject_cr_attachments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `cr_id` int(11) NOT NULL,
    `file_name` varchar(255) NOT NULL,
    `filetype` varchar(50) DEFAULT NULL,
    `uploaded_by` int(11) NOT NULL,
    `date_uploaded` datetime NOT NULL,
    PRIMARY KEY (`id`),
    KEY `cr_id` (`cr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

## Quick Debug

Add this to the top of `project_cr.php` to debug:

```php
// Debug mode - remove after testing
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Expected Behavior

1. **Module Activation**: Should complete without errors
2. **Project Tab**: "Change Requests" tab appears in project view
3. **Tab Content**: Shows statistics and table (initially empty)
4. **New CR Button**: Opens modal form
5. **Form Submission**: Creates new change request and refreshes table

## File Structure Verification

Ensure these files exist:
- `modules/project_cr/project_cr.php` ✓
- `modules/project_cr/install.php` ✓
- `modules/project_cr/controllers/Project_cr.php` ✓
- `modules/project_cr/models/Project_cr_model.php` ✓
- `modules/project_cr/views/project_change_requests.php` ✓
- `modules/project_cr/views/tables/project_change_requests.php` ✓
- `modules/project_cr/language/english/project_cr_lang.php` ✓

## Contact for Support

If issues persist:
1. Check PHP error logs
2. Verify database connectivity
3. Ensure proper file permissions
4. Check Perfex CRM version compatibility (2.3.0+)
