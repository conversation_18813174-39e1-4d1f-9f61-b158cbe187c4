<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Project Change Requests
Description: Module for managing change requests in projects
Version: 1.0.0
Requires at least: 2.3.*
*/

define('PROJECT_CR_MODULE_NAME', 'project_cr');

$CI = &get_instance();

/**
 * Register activation module hook
 */
register_activation_hook(PROJECT_CR_MODULE_NAME, 'project_cr_activation_hook');

function project_cr_activation_hook()
{
    require_once __DIR__ . '/install.php';
}

/**
 * Register language files, must be registered if the module is using languages
 */
register_language_files(PROJECT_CR_MODULE_NAME, [PROJECT_CR_MODULE_NAME]);

/**
 * Init project CR module hooks
 */
// Remove the sidebar menu - we only want the project tab
// hooks()->add_action('admin_init', 'project_cr_module_init_menu_items');
hooks()->add_action('admin_init', 'project_cr_permissions');
hooks()->add_action('admin_init', 'project_cr_init_project_tab');

/**
 * Load helper functions
 */
if (file_exists(__DIR__ . '/helpers/project_cr_helper.php')) {
    require_once __DIR__ . '/helpers/project_cr_helper.php';
}

/**
 * Add Change Requests tab to projects
 */
function project_cr_init_project_tab()
{
    $CI = &get_instance();

    $CI->app_tabs->add_project_tab('project_change_requests', [
        'name'     => function_exists('_l') ? _l('project_change_requests') : 'Change Requests',
        'icon'     => 'fa fa-exchange-alt',
        'view'     => 'project_cr/test_view',
        'position' => 45,
    ]);
}

/**
 * Register staff capabilities for change requests
 */
function project_cr_permissions()
{
    $capabilities = [];

    $capabilities['capabilities'] = [
        'view'   => function_exists('_l') ? _l('permission_view') . '(' . _l('permission_global') . ')' : 'View (Global)',
        'create' => function_exists('_l') ? _l('permission_create') : 'Create',
        'edit'   => function_exists('_l') ? _l('permission_edit') : 'Edit',
        'delete' => function_exists('_l') ? _l('permission_delete') : 'Delete',
    ];

    $name = function_exists('_l') ? _l('project_change_requests') : 'Project Change Requests';
    register_staff_capabilities('project_change_requests', $capabilities, $name);
}

/**
 * Init change requests module menu items in admin_init hook
 * @return null
 */
function project_cr_module_init_menu_items()
{
    $CI = &get_instance();

    if (function_exists('staff_can') && staff_can('view', 'project_change_requests')) {
        $CI->app_menu->add_sidebar_children_item('projects', [
            'slug'     => 'change-requests',
            'name'     => function_exists('_l') ? _l('change_requests') : 'Change Requests',
            'href'     => admin_url('project_cr'),
            'position' => 25,
        ]);
    }
}

/**
 * Get change request statuses
 * @return array
 */
function get_change_request_statuses()
{
    return [
        1 => [
            'id'    => 1,
            'name'  => function_exists('_l') ? _l('cr_status_draft') : 'Draft',
            'color' => '#6c757d',
        ],
        2 => [
            'id'    => 2,
            'name'  => function_exists('_l') ? _l('cr_status_submitted') : 'Submitted',
            'color' => '#007bff',
        ],
        3 => [
            'id'    => 3,
            'name'  => function_exists('_l') ? _l('cr_status_approved') : 'Approved',
            'color' => '#28a745',
        ],
        4 => [
            'id'    => 4,
            'name'  => function_exists('_l') ? _l('cr_status_rejected') : 'Rejected',
            'color' => '#dc3545',
        ],
        5 => [
            'id'    => 5,
            'name'  => function_exists('_l') ? _l('cr_status_implemented') : 'Implemented',
            'color' => '#17a2b8',
        ],
    ];
}

/**
 * Get change request status by ID
 * @param int $status_id
 * @return array|null
 */
function get_change_request_status($status_id)
{
    $statuses = get_change_request_statuses();
    return isset($statuses[$status_id]) ? $statuses[$status_id] : null;
}

/**
 * Format change request status for display
 * @param int $status_id
 * @return string
 */
function format_change_request_status($status_id)
{
    $status = get_change_request_status($status_id);
    if (!$status) {
        return '';
    }
    
    return '<span class="label" style="background-color: ' . $status['color'] . '">' . $status['name'] . '</span>';
}
