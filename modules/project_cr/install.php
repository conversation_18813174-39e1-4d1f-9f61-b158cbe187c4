<?php

defined('BASEPATH') or exit('No direct script access allowed');

// Create the change requests table
if (!$CI->db->table_exists(db_prefix() . 'project_change_requests')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . 'project_change_requests` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `project_id` int(11) NOT NULL,
        `title` varchar(255) NOT NULL,
        `description` text NOT NULL,
        `justification` text,
        `impact_analysis` text,
        `estimated_cost` decimal(15,2) DEFAULT NULL,
        `estimated_hours` decimal(8,2) DEFAULT NULL,
        `priority` tinyint(1) NOT NULL DEFAULT 2 COMMENT "1=Low, 2=Medium, 3=High, 4=Critical",
        `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT "1=Draft, 2=Submitted, 3=Approved, 4=Rejected, 5=Implemented",
        `requested_by` int(11) NOT NULL,
        `assigned_to` int(11) DEFAULT NULL,
        `approved_by` int(11) DEFAULT NULL,
        `approved_date` datetime DEFAULT NULL,
        `implementation_date` datetime DEFAULT NULL,
        `rejection_reason` text,
        `date_created` datetime NOT NULL,
        `date_updated` datetime DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `project_id` (`project_id`),
        KEY `requested_by` (`requested_by`),
        KEY `assigned_to` (`assigned_to`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
}

// Create change request comments table
if (!$CI->db->table_exists(db_prefix() . 'project_cr_comments')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . 'project_cr_comments` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `cr_id` int(11) NOT NULL,
        `staff_id` int(11) NOT NULL,
        `comment` text NOT NULL,
        `date_created` datetime NOT NULL,
        PRIMARY KEY (`id`),
        KEY `cr_id` (`cr_id`),
        KEY `staff_id` (`staff_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
}

// Create change request attachments table
if (!$CI->db->table_exists(db_prefix() . 'project_cr_attachments')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . 'project_cr_attachments` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `cr_id` int(11) NOT NULL,
        `file_name` varchar(255) NOT NULL,
        `filetype` varchar(50) DEFAULT NULL,
        `uploaded_by` int(11) NOT NULL,
        `date_uploaded` datetime NOT NULL,
        PRIMARY KEY (`id`),
        KEY `cr_id` (`cr_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set . ';');
}

// Add module options
add_option('project_cr_auto_assign_to_pm', '1');
add_option('project_cr_email_notifications', '1');
add_option('project_cr_require_approval', '1');
