# Emergency Fix for Project Change Requests Module

## Current Status
The module has been updated with a simple test view to diagnose the issue.

## Test Steps

1. **Deactivate and Reactivate Module**:
   - Go to Setup → Modules
   - Find "Project Change Requests"
   - Click "Deactivate" if it's active
   - Click "Activate" again

2. **Test Simple View**:
   - Go to any project
   - Look for "Change Requests" tab
   - Click on it - you should see a simple test message

## If Test View Works

If the simple test view loads successfully, the issue is with the complex view. To fix:

1. **Restore Original View**:
   Edit `modules/project_cr/project_cr.php` line 56:
   ```php
   'view' => 'project_cr/project_change_requests',
   ```

2. **Check for Specific Issues**:
   - PHP syntax errors in the view
   - Missing functions or variables
   - JavaScript errors

## If Test View Doesn't Work

If even the simple view doesn't work, there's a fundamental issue:

1. **Check Module Activation**:
   - Ensure module is properly activated
   - Check PHP error logs for activation errors

2. **Check File Permissions**:
   ```bash
   chmod -R 755 modules/project_cr/
   ```

3. **Check Database Tables**:
   ```sql
   SHOW TABLES LIKE '%project_change_requests%';
   ```

## Quick Fixes

### Fix 1: Minimal Working Version
Replace the content of `modules/project_cr/project_cr.php` with this minimal version:

```php
<?php
defined('BASEPATH') or exit('No direct script access allowed');

define('PROJECT_CR_MODULE_NAME', 'project_cr');

register_activation_hook(PROJECT_CR_MODULE_NAME, 'project_cr_activation_hook');

function project_cr_activation_hook()
{
    require_once __DIR__ . '/install.php';
}

register_language_files(PROJECT_CR_MODULE_NAME, [PROJECT_CR_MODULE_NAME]);

hooks()->add_action('admin_init', 'project_cr_init_project_tab');

function project_cr_init_project_tab()
{
    $CI = &get_instance();
    
    if (isset($CI->app_tabs)) {
        $CI->app_tabs->add_project_tab('project_change_requests', [
            'name'     => 'Change Requests',
            'icon'     => 'fa fa-exchange-alt',
            'view'     => 'project_cr/test_view',
            'position' => 45,
        ]);
    }
}
```

### Fix 2: Alternative View Path
If the module view path doesn't work, try using a direct path:

```php
'view' => APPPATH . '../modules/project_cr/views/test_view.php',
```

### Fix 3: Hook-based Loading
Instead of using the tab system, use a hook to inject content:

```php
hooks()->add_action('before_render_project_view', 'project_cr_inject_tab');

function project_cr_inject_tab($project_id)
{
    // Inject tab content using JavaScript or direct HTML
}
```

## Debugging Commands

1. **Check if module is loaded**:
   ```php
   // Add this to any view to debug
   var_dump(get_instance()->app_modules->get_activated());
   ```

2. **Check if tabs are registered**:
   ```php
   // Add this to debug tabs
   var_dump(get_instance()->app_tabs->get_project_tabs());
   ```

3. **Check view path resolution**:
   ```php
   // Add this to debug view paths
   echo module_views_path('project_cr', 'test_view');
   ```

## Recovery Steps

If the module breaks other functionality:

1. **Disable Module**:
   ```sql
   UPDATE tblmodules SET active = 0 WHERE module_name = 'project_cr';
   ```

2. **Remove Module Files** (if necessary):
   ```bash
   rm -rf modules/project_cr/
   ```

3. **Clear Cache** (if any):
   - Clear browser cache
   - Clear any PHP opcache
   - Restart web server if needed

## Contact Information

If none of these fixes work:
1. Check PHP error logs for specific errors
2. Verify Perfex CRM version compatibility
3. Test with a fresh Perfex CRM installation
4. Contact Perfex CRM support for module development guidelines

## Current Module State

The module is currently set to use a simple test view (`project_cr/test_view`) to verify basic functionality. Once this works, we can gradually add complexity back to identify the specific issue.
