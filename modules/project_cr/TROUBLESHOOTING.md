# Project Change Requests Module - Troubleshooting

## HTTP 500 Error During Activation

If you're getting an HTTP 500 error when trying to activate the module, follow these steps:

### Step 1: Check PHP Error Logs

1. **Enable PHP Error Logging** (if not already enabled):
   - Add these lines to your `.htaccess` file or `php.ini`:
   ```
   php_flag log_errors on
   php_value error_log /path/to/your/error.log
   ```

2. **Check Error Logs**:
   - Look in your server's error log (usually in `/var/log/apache2/error.log` or similar)
   - Or check Perfex CRM's error log if configured
   - Look for any PHP fatal errors, syntax errors, or database errors

### Step 2: Check Database Permissions

1. **Verify Database User Permissions**:
   - Ensure your database user has `CREATE TABLE` permissions
   - Test with this SQL command:
   ```sql
   CREATE TABLE test_table (id INT AUTO_INCREMENT PRIMARY KEY);
   DROP TABLE test_table;
   ```

2. **Check Database Connection**:
   - Verify your database connection is working
   - Check `application/config/database.php` settings

### Step 3: Manual Debugging

1. **Add Debug Code to install.php**:
   Add this at the beginning of `modules/project_cr/install.php`:
   ```php
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   echo "Starting module installation...\n";
   ```

2. **Test Database Table Creation**:
   Try creating the tables manually in your database:
   ```sql
   CREATE TABLE `tblproject_change_requests` (
       `id` int(11) NOT NULL AUTO_INCREMENT,
       `project_id` int(11) NOT NULL,
       `title` varchar(255) NOT NULL,
       `description` text NOT NULL,
       `justification` text,
       `impact_analysis` text,
       `estimated_cost` decimal(15,2) DEFAULT NULL,
       `estimated_hours` decimal(8,2) DEFAULT NULL,
       `priority` tinyint(1) NOT NULL DEFAULT 2,
       `status` tinyint(1) NOT NULL DEFAULT 1,
       `requested_by` int(11) NOT NULL,
       `assigned_to` int(11) DEFAULT NULL,
       `approved_by` int(11) DEFAULT NULL,
       `approved_date` datetime DEFAULT NULL,
       `implementation_date` datetime DEFAULT NULL,
       `rejection_reason` text,
       `date_created` datetime NOT NULL,
       `date_updated` datetime DEFAULT NULL,
       PRIMARY KEY (`id`),
       KEY `project_id` (`project_id`),
       KEY `requested_by` (`requested_by`),
       KEY `assigned_to` (`assigned_to`),
       KEY `status` (`status`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
   ```

### Step 4: Check File Permissions

1. **Verify File Permissions**:
   ```bash
   chmod -R 755 modules/project_cr/
   chown -R www-data:www-data modules/project_cr/
   ```

2. **Check if all files exist**:
   ```bash
   ls -la modules/project_cr/
   ls -la modules/project_cr/controllers/
   ls -la modules/project_cr/models/
   ls -la modules/project_cr/views/
   ```

### Step 5: Simplified Installation

If the above doesn't work, try this simplified approach:

1. **Temporarily disable hooks** in `project_cr.php`:
   Comment out these lines:
   ```php
   // hooks()->add_action('admin_init', 'project_cr_module_init_menu_items');
   // hooks()->add_action('admin_init', 'project_cr_permissions');
   // hooks()->add_action('admin_init', 'project_cr_init_project_tab');
   ```

2. **Activate the module** with minimal functionality

3. **Re-enable hooks** one by one to identify the problematic function

### Step 6: Check Perfex CRM Version

Ensure you're running Perfex CRM version 2.3.0 or higher:
1. Check `application/config/app.php` for version number
2. Or check the admin dashboard footer

### Step 7: Common Issues and Solutions

**Issue**: "Call to undefined function _l()"
**Solution**: Language functions not loaded yet. This is fixed in the updated module files.

**Issue**: "Call to undefined function staff_can()"
**Solution**: Staff functions not loaded yet. This is fixed in the updated module files.

**Issue**: "Table already exists" error
**Solution**: Drop existing tables and try again:
```sql
DROP TABLE IF EXISTS `tblproject_change_requests`;
DROP TABLE IF EXISTS `tblproject_cr_comments`;
DROP TABLE IF EXISTS `tblproject_cr_attachments`;
```

**Issue**: "Class 'AdminController' not found"
**Solution**: Ensure the controller extends the correct base class and the file is in the right location.

### Step 8: Safe Mode Installation

If all else fails, try installing in "safe mode":

1. **Create a minimal version** of `project_cr.php`:
   ```php
   <?php
   defined('BASEPATH') or exit('No direct script access allowed');
   
   /*
   Module Name: Project Change Requests
   Description: Module for managing change requests in projects
   Version: 1.0.0
   Requires at least: 2.3.*
   */
   
   define('PROJECT_CR_MODULE_NAME', 'project_cr');
   
   register_activation_hook(PROJECT_CR_MODULE_NAME, 'project_cr_activation_hook');
   
   function project_cr_activation_hook()
   {
       require_once __DIR__ . '/install.php';
   }
   ```

2. **Activate this minimal version first**

3. **Gradually add functionality** by uncommenting sections

### Getting Help

If you're still experiencing issues:

1. **Check the exact error message** in your PHP error logs
2. **Note your server environment**:
   - PHP version
   - MySQL version
   - Perfex CRM version
   - Server OS

3. **Provide the error details** when seeking help

### Emergency Cleanup

If the module gets stuck in a bad state:

1. **Remove from database**:
   ```sql
   DELETE FROM `tblmodules` WHERE `module_name` = 'project_cr';
   ```

2. **Remove module files**:
   ```bash
   rm -rf modules/project_cr/
   ```

3. **Start fresh** with a clean installation
