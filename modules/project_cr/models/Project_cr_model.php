<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Project_cr_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get change request(s)
     * @param  mixed $id change request id
     * @param  array $where additional where conditions
     * @return mixed
     */
    public function get($id = '', $where = [])
    {
        $this->db->select('
            ' . db_prefix() . 'project_change_requests.*,
            ' . db_prefix() . 'projects.name as project_name,
            CONCAT(requested_staff.firstname, " ", requested_staff.lastname) as requested_by_name,
            CONCAT(assigned_staff.firstname, " ", assigned_staff.lastname) as assigned_to_name,
            CONCAT(approved_staff.firstname, " ", approved_staff.lastname) as approved_by_name
        ');
        
        $this->db->from(db_prefix() . 'project_change_requests');
        $this->db->join(db_prefix() . 'projects', db_prefix() . 'projects.id = ' . db_prefix() . 'project_change_requests.project_id', 'left');
        $this->db->join(db_prefix() . 'staff as requested_staff', 'requested_staff.staffid = ' . db_prefix() . 'project_change_requests.requested_by', 'left');
        $this->db->join(db_prefix() . 'staff as assigned_staff', 'assigned_staff.staffid = ' . db_prefix() . 'project_change_requests.assigned_to', 'left');
        $this->db->join(db_prefix() . 'staff as approved_staff', 'approved_staff.staffid = ' . db_prefix() . 'project_change_requests.approved_by', 'left');

        if (is_array($where) && count($where) > 0) {
            $this->db->where($where);
        }

        if (is_numeric($id)) {
            $this->db->where(db_prefix() . 'project_change_requests.id', $id);
            return $this->db->get()->row();
        }

        $this->db->order_by('date_created', 'DESC');
        return $this->db->get()->result_array();
    }

    /**
     * Get change requests by project ID
     * @param  int $project_id
     * @return array
     */
    public function get_by_project($project_id)
    {
        return $this->get('', ['project_id' => $project_id]);
    }

    /**
     * Add new change request
     * @param array $data change request data
     * @return mixed
     */
    public function add($data)
    {
        $data = $this->check_zero_columns($data);
        
        $this->db->insert(db_prefix() . 'project_change_requests', $data);
        $insert_id = $this->db->insert_id();

        if ($insert_id) {
            hooks()->do_action('change_request_created', $insert_id);
            log_activity('New Change Request Created [ID: ' . $insert_id . ']');
        }

        return $insert_id;
    }

    /**
     * Update change request
     * @param  array $data change request data
     * @param  mixed $id   change request id
     * @return boolean
     */
    public function update($data, $id)
    {
        $data = $this->check_zero_columns($data);
        
        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'project_change_requests', $data);

        if ($this->db->affected_rows() > 0) {
            hooks()->do_action('change_request_updated', $id);
            log_activity('Change Request Updated [ID: ' . $id . ']');
            return true;
        }

        return false;
    }

    /**
     * Delete change request
     * @param  mixed $id change request id
     * @return boolean
     */
    public function delete($id)
    {
        hooks()->do_action('before_change_request_deleted', $id);

        $this->db->where('id', $id);
        $this->db->delete(db_prefix() . 'project_change_requests');

        if ($this->db->affected_rows() > 0) {
            // Delete related comments
            $this->db->where('cr_id', $id);
            $this->db->delete(db_prefix() . 'project_cr_comments');

            // Delete related attachments
            $this->db->where('cr_id', $id);
            $this->db->delete(db_prefix() . 'project_cr_attachments');

            hooks()->do_action('change_request_deleted', $id);
            log_activity('Change Request Deleted [ID: ' . $id . ']');
            return true;
        }

        return false;
    }

    /**
     * Get change request comments
     * @param  int $cr_id
     * @return array
     */
    public function get_comments($cr_id)
    {
        $this->db->select('
            ' . db_prefix() . 'project_cr_comments.*,
            CONCAT(' . db_prefix() . 'staff.firstname, " ", ' . db_prefix() . 'staff.lastname) as staff_name,
            ' . db_prefix() . 'staff.profile_image
        ');
        
        $this->db->from(db_prefix() . 'project_cr_comments');
        $this->db->join(db_prefix() . 'staff', db_prefix() . 'staff.staffid = ' . db_prefix() . 'project_cr_comments.staff_id');
        $this->db->where('cr_id', $cr_id);
        $this->db->order_by('date_created', 'ASC');

        return $this->db->get()->result_array();
    }

    /**
     * Add comment to change request
     * @param array $data comment data
     * @return boolean
     */
    public function add_comment($data)
    {
        $this->db->insert(db_prefix() . 'project_cr_comments', $data);
        
        if ($this->db->affected_rows() > 0) {
            log_activity('Comment Added to Change Request [CR ID: ' . $data['cr_id'] . ']');
            return true;
        }

        return false;
    }

    /**
     * Get change request statistics
     * @param  int $project_id optional project filter
     * @return array
     */
    public function get_statistics($project_id = null)
    {
        $where = '';
        if ($project_id) {
            $where = 'WHERE project_id = ' . (int)$project_id;
        }

        $sql = 'SELECT 
                    status,
                    COUNT(*) as count
                FROM ' . db_prefix() . 'project_change_requests 
                ' . $where . '
                GROUP BY status';

        $result = $this->db->query($sql)->result_array();
        
        $stats = [
            'total' => 0,
            'draft' => 0,
            'submitted' => 0,
            'approved' => 0,
            'rejected' => 0,
            'implemented' => 0
        ];

        foreach ($result as $row) {
            $stats['total'] += $row['count'];
            
            switch ($row['status']) {
                case 1:
                    $stats['draft'] = $row['count'];
                    break;
                case 2:
                    $stats['submitted'] = $row['count'];
                    break;
                case 3:
                    $stats['approved'] = $row['count'];
                    break;
                case 4:
                    $stats['rejected'] = $row['count'];
                    break;
                case 5:
                    $stats['implemented'] = $row['count'];
                    break;
            }
        }

        return $stats;
    }

    /**
     * Check for zero columns and convert to NULL
     * @param  array $data
     * @return array
     */
    private function check_zero_columns($data)
    {
        $zero_columns = ['assigned_to', 'approved_by', 'estimated_cost', 'estimated_hours'];
        
        foreach ($zero_columns as $column) {
            if (isset($data[$column]) && ($data[$column] == '' || $data[$column] == 0)) {
                $data[$column] = null;
            }
        }

        return $data;
    }
}
