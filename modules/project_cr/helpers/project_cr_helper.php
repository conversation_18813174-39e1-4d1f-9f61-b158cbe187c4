<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Get change request priority options
 * @return array
 */
function get_change_request_priorities()
{
    return [
        1 => [
            'id' => 1,
            'name' => function_exists('_l') ? _l('cr_priority_low') : 'Low',
            'color' => '#28a745'
        ],
        2 => [
            'id' => 2,
            'name' => function_exists('_l') ? _l('cr_priority_medium') : 'Medium',
            'color' => '#ffc107'
        ],
        3 => [
            'id' => 3,
            'name' => function_exists('_l') ? _l('cr_priority_high') : 'High',
            'color' => '#fd7e14'
        ],
        4 => [
            'id' => 4,
            'name' => function_exists('_l') ? _l('cr_priority_critical') : 'Critical',
            'color' => '#dc3545'
        ]
    ];
}

/**
 * Get change request priority by ID
 * @param int $priority_id
 * @return array|null
 */
function get_change_request_priority($priority_id)
{
    $priorities = get_change_request_priorities();
    return isset($priorities[$priority_id]) ? $priorities[$priority_id] : null;
}

/**
 * Format change request priority for display
 * @param int $priority_id
 * @return string
 */
function format_change_request_priority($priority_id)
{
    $priority = get_change_request_priority($priority_id);
    if (!$priority) {
        return '';
    }
    
    return '<span class="label" style="background-color: ' . $priority['color'] . '">' . $priority['name'] . '</span>';
}

/**
 * Check if user can view change request
 * @param int $cr_id
 * @param int $staff_id
 * @return boolean
 */
function can_view_change_request($cr_id, $staff_id = null)
{
    if (!function_exists('get_staff_user_id') || !function_exists('staff_can')) {
        return false;
    }

    if (!$staff_id) {
        $staff_id = get_staff_user_id();
    }

    if (staff_can('view', 'project_change_requests')) {
        return true;
    }

    // Check if user is assigned to the change request or is the requester
    $CI = &get_instance();
    if (!$CI) {
        return false;
    }

    $CI->load->model('project_cr/project_cr_model');

    $cr = $CI->project_cr_model->get($cr_id);
    if ($cr && ($cr->requested_by == $staff_id || $cr->assigned_to == $staff_id)) {
        return true;
    }

    return false;
}

/**
 * Check if user can edit change request
 * @param int $cr_id
 * @param int $staff_id
 * @return boolean
 */
function can_edit_change_request($cr_id, $staff_id = null)
{
    if (!function_exists('get_staff_user_id') || !function_exists('staff_can')) {
        return false;
    }

    if (!$staff_id) {
        $staff_id = get_staff_user_id();
    }

    if (staff_can('edit', 'project_change_requests')) {
        return true;
    }

    // Check if user is the requester and CR is still in draft
    $CI = &get_instance();
    if (!$CI) {
        return false;
    }

    $CI->load->model('project_cr/project_cr_model');

    $cr = $CI->project_cr_model->get($cr_id);
    if ($cr && $cr->requested_by == $staff_id && $cr->status == 1) {
        return true;
    }

    return false;
}
