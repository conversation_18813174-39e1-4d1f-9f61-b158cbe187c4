# Project Change Requests Module - Installation Guide

## Prerequisites

- Perfex CRM version 2.3.0 or higher
- Admin access to your Perfex CRM installation
- FTP/File manager access to upload files

## Installation Steps

### Method 1: Manual Installation

1. **Upload Files**
   - Download/copy the entire `project_cr` folder
   - Upload it to your Perfex CRM `modules` directory
   - The final path should be: `/path/to/perfex/modules/project_cr/`

2. **Set Permissions**
   - Ensure the uploaded files have proper read permissions
   - The web server should be able to read all files in the module directory

3. **Install via Admin Panel**
   - Log in to your Perfex CRM as an administrator
   - Navigate to **Setup → Modules**
   - Find "Project Change Requests" in the list
   - Click the **Install** button
   - Once installed, click **Activate** to enable the module

### Method 2: ZIP Upload (if supported)

1. **Create ZIP Archive**
   - Compress the entire `project_cr` folder into a ZIP file
   - Make sure the folder structure is preserved

2. **Upload via Admin Panel**
   - Go to **Setup → Modules**
   - Click **Upload Module**
   - Select your ZIP file and upload
   - The module will be automatically extracted and installed

## Post-Installation

### 1. Configure Permissions

After installation, configure staff permissions:

1. Go to **Setup → Staff → Roles**
2. Edit each role that should have access to change requests
3. Under "Project Change Requests" section, set appropriate permissions:
   - **View**: Allow viewing change requests
   - **Create**: Allow creating new change requests
   - **Edit**: Allow editing existing change requests
   - **Delete**: Allow deleting change requests

### 2. Verify Installation

1. Navigate to any project in your CRM
2. You should see a new "Change Requests" tab
3. Go to **Projects** menu - you should see a "Change Requests" submenu item
4. Test creating a new change request

### 3. Optional Configuration

The module includes these default settings that can be customized:
- Auto-assign change requests to project manager
- Email notifications for status changes
- Require approval workflow

## Database Tables Created

The module automatically creates these tables:
- `{prefix}project_change_requests` - Main change request data
- `{prefix}project_cr_comments` - Comments on change requests
- `{prefix}project_cr_attachments` - File attachments (for future use)

## Troubleshooting

### Module Not Appearing

1. Check file permissions - ensure web server can read the files
2. Verify the folder structure is correct
3. Check PHP error logs for any syntax errors
4. Ensure Perfex CRM version compatibility

### Permission Errors

1. Make sure staff roles have appropriate permissions set
2. Check if the user is assigned to the project (for project-specific access)

### Database Errors

1. Ensure your database user has CREATE TABLE permissions
2. Check if table prefix is correctly configured
3. Verify database connection is working

### Tab Not Showing in Projects

1. Clear any caches if you have caching enabled
2. Check if the project has the change requests feature enabled
3. Verify the module is activated (not just installed)

## Uninstallation

To remove the module:

1. Go to **Setup → Modules**
2. Find "Project Change Requests"
3. Click **Deactivate** first
4. Then click **Uninstall**
5. Manually delete the `modules/project_cr` folder if needed

**Note**: Uninstalling will remove all change request data from the database.

## Support

If you encounter issues during installation:

1. Check the Perfex CRM error logs
2. Verify all file permissions are correct
3. Ensure database connectivity and permissions
4. Contact your system administrator or developer for assistance

## File Structure Reference

```
modules/project_cr/
├── project_cr.php              # Main module file
├── install.php                 # Installation script
├── README.md                   # Module documentation
├── INSTALLATION.md             # This file
├── controllers/
│   ├── Project_cr.php          # Main controller
│   └── index.html              # Security file
├── models/
│   ├── Project_cr_model.php    # Data model
│   └── index.html              # Security file
├── views/
│   ├── manage.php              # Main management view
│   ├── change_request.php      # CR form view
│   ├── project_change_requests.php # Project tab view
│   ├── tables/                 # DataTable views
│   └── index.html              # Security file
├── helpers/
│   ├── project_cr_helper.php   # Helper functions
│   └── index.html              # Security file
├── language/
│   └── english/
│       ├── project_cr_lang.php # English translations
│       └── index.html          # Security file
└── index.html                  # Security file
```
