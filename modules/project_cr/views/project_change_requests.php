<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="row">
    <div class="col-md-12">
        <div class="panel_s">
            <div class="panel-body">
                <div class="_buttons">
                    <?php if (staff_can('create', 'project_change_requests')) { ?>
                    <a href="#" class="btn btn-info pull-left" data-toggle="modal" data-target="#new-cr-modal">
                        <?php echo _l('new_change_request'); ?>
                    </a>
                    <?php } ?>
                    <div class="clearfix"></div>
                </div>
                <div class="clearfix"></div>
                <hr class="hr-panel-heading" />
                
                <!-- Statistics for this project -->
                <div class="row">
                    <div class="col-md-2 col-sm-6">
                        <div class="tw-bg-neutral-50 tw-border tw-border-solid tw-border-neutral-200 tw-rounded-lg tw-p-3 tw-text-center">
                            <h4 class="tw-text-lg tw-font-semibold tw-text-neutral-800 tw-mb-1" id="project-total-crs">0</h4>
                            <p class="tw-text-xs tw-text-neutral-600 tw-mb-0"><?php echo _l('total_change_requests'); ?></p>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="tw-bg-blue-50 tw-border tw-border-solid tw-border-blue-200 tw-rounded-lg tw-p-3 tw-text-center">
                            <h4 class="tw-text-lg tw-font-semibold tw-text-blue-600 tw-mb-1" id="project-submitted-crs">0</h4>
                            <p class="tw-text-xs tw-text-blue-600 tw-mb-0"><?php echo _l('submitted_change_requests'); ?></p>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="tw-bg-green-50 tw-border tw-border-solid tw-border-green-200 tw-rounded-lg tw-p-3 tw-text-center">
                            <h4 class="tw-text-lg tw-font-semibold tw-text-green-600 tw-mb-1" id="project-approved-crs">0</h4>
                            <p class="tw-text-xs tw-text-green-600 tw-mb-0"><?php echo _l('approved_change_requests'); ?></p>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="tw-bg-red-50 tw-border tw-border-solid tw-border-red-200 tw-rounded-lg tw-p-3 tw-text-center">
                            <h4 class="tw-text-lg tw-font-semibold tw-text-red-600 tw-mb-1" id="project-rejected-crs">0</h4>
                            <p class="tw-text-xs tw-text-red-600 tw-mb-0"><?php echo _l('rejected_change_requests'); ?></p>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="tw-bg-teal-50 tw-border tw-border-solid tw-border-teal-200 tw-rounded-lg tw-p-3 tw-text-center">
                            <h4 class="tw-text-lg tw-font-semibold tw-text-teal-600 tw-mb-1" id="project-implemented-crs">0</h4>
                            <p class="tw-text-xs tw-text-teal-600 tw-mb-0"><?php echo _l('implemented_change_requests'); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="clearfix"></div>
                <hr class="hr-panel-heading" />
                
                <!-- Change Requests Table -->
                <table class="table dt-table table-project-change-requests" data-order-col="4" data-order-type="desc">
                    <thead>
                        <tr>
                            <th><?php echo _l('cr_table_title'); ?></th>
                            <th><?php echo _l('cr_table_priority'); ?></th>
                            <th><?php echo _l('cr_table_status'); ?></th>
                            <th><?php echo _l('cr_table_requested_by'); ?></th>
                            <th><?php echo _l('cr_table_date_created'); ?></th>
                            <th><?php echo _l('options'); ?></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- New Change Request Modal -->
<div class="modal fade" id="new-cr-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('new_change_request'); ?></h4>
            </div>
            <?php echo form_open(admin_url('project_cr/add_project_cr'), ['id' => 'new-cr-form']); ?>
            <div class="modal-body">
                <input type="hidden" name="project_id" value="<?php echo $project->id; ?>">
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="cr_title" class="control-label">
                                <?php echo _l('cr_title'); ?> <span class="text-danger">*</span>
                            </label>
                            <input type="text" id="cr_title" name="title" class="form-control" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="cr_priority" class="control-label"><?php echo _l('cr_priority'); ?></label>
                            <select name="priority" id="cr_priority" class="form-control selectpicker">
                                <?php foreach (get_change_request_priorities() as $priority) { ?>
                                <option value="<?php echo $priority['id']; ?>" <?php if ($priority['id'] == 2) echo 'selected'; ?>>
                                    <?php echo $priority['name']; ?>
                                </option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="cr_assigned_to" class="control-label"><?php echo _l('cr_assigned_to'); ?></label>
                            <select name="assigned_to" id="cr_assigned_to" class="form-control selectpicker" data-live-search="true">
                                <option value=""><?php echo _l('not_assigned'); ?></option>
                                <?php 
                                $this->load->model('staff_model');
                                $staff_members = $this->staff_model->get();
                                foreach ($staff_members as $staff) { ?>
                                <option value="<?php echo $staff['staffid']; ?>">
                                    <?php echo $staff['firstname'] . ' ' . $staff['lastname']; ?>
                                </option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="cr_estimated_cost" class="control-label"><?php echo _l('cr_estimated_cost'); ?></label>
                            <input type="number" step="0.01" id="cr_estimated_cost" name="estimated_cost" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="cr_estimated_hours" class="control-label"><?php echo _l('cr_estimated_hours'); ?></label>
                            <input type="number" step="0.25" id="cr_estimated_hours" name="estimated_hours" class="form-control">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="cr_description" class="control-label">
                                <?php echo _l('cr_description'); ?> <span class="text-danger">*</span>
                            </label>
                            <textarea id="cr_description" name="description" class="form-control" rows="4" required></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="cr_justification" class="control-label"><?php echo _l('cr_justification'); ?></label>
                            <textarea id="cr_justification" name="justification" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                <button type="submit" class="btn btn-info"><?php echo _l('submit'); ?></button>
            </div>
            <?php echo form_close(); ?>
        </div>
    </div>
</div>

<script>
$(function() {
    // Initialize DataTable for project change requests
    var projectId = <?php echo $project->id; ?>;
    
    initDataTable('.table-project-change-requests', admin_url + 'project_cr/project_table/' + projectId, [5], [5], {}, [4, 'desc']);
    
    // Load project statistics
    loadProjectStatistics(projectId);
    
    // Form validation
    appValidateForm($('#new-cr-form'), {
        title: 'required',
        description: 'required'
    }, function(form) {
        form.find('button[type="submit"]').prop('disabled', true);
        return true;
    });
    
    // Handle form submission
    $('#new-cr-form').on('submit', function(e) {
        e.preventDefault();
        
        $.post($(this).attr('action'), $(this).serialize())
            .done(function(response) {
                if (response.success) {
                    alert_float('success', response.message);
                    $('#new-cr-modal').modal('hide');
                    $('.table-project-change-requests').DataTable().ajax.reload();
                    loadProjectStatistics(projectId);
                    $('#new-cr-form')[0].reset();
                    $('#new-cr-form').find('.selectpicker').selectpicker('refresh');
                } else {
                    alert_float('danger', response.message);
                }
                $('#new-cr-form').find('button[type="submit"]').prop('disabled', false);
            })
            .fail(function() {
                alert_float('danger', 'Something went wrong');
                $('#new-cr-form').find('button[type="submit"]').prop('disabled', false);
            });
    });
});

function loadProjectStatistics(projectId) {
    $.get(admin_url + 'project_cr/get_project_statistics/' + projectId, function(response) {
        if (response.success) {
            $('#project-total-crs').text(response.data.total);
            $('#project-submitted-crs').text(response.data.submitted);
            $('#project-approved-crs').text(response.data.approved);
            $('#project-rejected-crs').text(response.data.rejected);
            $('#project-implemented-crs').text(response.data.implemented);
        }
    }, 'json');
}

function updateStatus(crId, status) {
    if (confirm('Are you sure you want to update the status?')) {
        $.post(admin_url + 'project_cr/update_status', {
            id: crId,
            status: status
        })
        .done(function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('.table-project-change-requests').DataTable().ajax.reload();
                loadProjectStatistics(<?php echo $project->id; ?>);
            } else {
                alert_float('danger', response.message);
            }
        })
        .fail(function() {
            alert_float('danger', 'Something went wrong');
        });
    }
}
</script>
