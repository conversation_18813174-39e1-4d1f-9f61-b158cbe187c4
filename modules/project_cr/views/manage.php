<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="_buttons">
                            <?php if (staff_can('create', 'project_change_requests')) { ?>
                            <a href="<?php echo admin_url('project_cr/change_request'); ?>" class="btn btn-info pull-left display-block">
                                <?php echo _l('new_change_request'); ?>
                            </a>
                            <?php } ?>
                            <div class="clearfix"></div>
                        </div>
                        <div class="clearfix"></div>
                        <hr class="hr-panel-heading" />
                        
                        <!-- Statistics Cards -->
                        <div class="row">
                            <div class="col-md-2 col-sm-6">
                                <div class="tw-bg-neutral-50 tw-border tw-border-solid tw-border-neutral-200 tw-rounded-lg tw-p-4 tw-text-center">
                                    <h3 class="tw-text-2xl tw-font-semibold tw-text-neutral-800 tw-mb-1" id="total-crs">0</h3>
                                    <p class="tw-text-sm tw-text-neutral-600 tw-mb-0"><?php echo _l('total_change_requests'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="tw-bg-gray-50 tw-border tw-border-solid tw-border-gray-200 tw-rounded-lg tw-p-4 tw-text-center">
                                    <h3 class="tw-text-2xl tw-font-semibold tw-text-gray-600 tw-mb-1" id="draft-crs">0</h3>
                                    <p class="tw-text-sm tw-text-gray-600 tw-mb-0"><?php echo _l('draft_change_requests'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="tw-bg-blue-50 tw-border tw-border-solid tw-border-blue-200 tw-rounded-lg tw-p-4 tw-text-center">
                                    <h3 class="tw-text-2xl tw-font-semibold tw-text-blue-600 tw-mb-1" id="submitted-crs">0</h3>
                                    <p class="tw-text-sm tw-text-blue-600 tw-mb-0"><?php echo _l('submitted_change_requests'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="tw-bg-green-50 tw-border tw-border-solid tw-border-green-200 tw-rounded-lg tw-p-4 tw-text-center">
                                    <h3 class="tw-text-2xl tw-font-semibold tw-text-green-600 tw-mb-1" id="approved-crs">0</h3>
                                    <p class="tw-text-sm tw-text-green-600 tw-mb-0"><?php echo _l('approved_change_requests'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="tw-bg-red-50 tw-border tw-border-solid tw-border-red-200 tw-rounded-lg tw-p-4 tw-text-center">
                                    <h3 class="tw-text-2xl tw-font-semibold tw-text-red-600 tw-mb-1" id="rejected-crs">0</h3>
                                    <p class="tw-text-sm tw-text-red-600 tw-mb-0"><?php echo _l('rejected_change_requests'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="tw-bg-teal-50 tw-border tw-border-solid tw-border-teal-200 tw-rounded-lg tw-p-4 tw-text-center">
                                    <h3 class="tw-text-2xl tw-font-semibold tw-text-teal-600 tw-mb-1" id="implemented-crs">0</h3>
                                    <p class="tw-text-sm tw-text-teal-600 tw-mb-0"><?php echo _l('implemented_change_requests'); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="clearfix"></div>
                        <hr class="hr-panel-heading" />
                        
                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_status"><?php echo _l('cr_status'); ?></label>
                                    <select name="filter_status" id="filter_status" class="form-control selectpicker" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>">
                                        <option value=""><?php echo _l('all'); ?></option>
                                        <?php foreach (get_change_request_statuses() as $status) { ?>
                                        <option value="<?php echo $status['id']; ?>"><?php echo $status['name']; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_priority"><?php echo _l('cr_priority'); ?></label>
                                    <select name="filter_priority" id="filter_priority" class="form-control selectpicker" data-none-selected-text="<?php echo _l('dropdown_non_selected_tex'); ?>">
                                        <option value=""><?php echo _l('all'); ?></option>
                                        <?php foreach (get_change_request_priorities() as $priority) { ?>
                                        <option value="<?php echo $priority['id']; ?>"><?php echo $priority['name']; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="clearfix"></div>
                        
                        <!-- Data Table -->
                        <table class="table dt-table table-change-requests" data-order-col="5" data-order-type="desc">
                            <thead>
                                <tr>
                                    <th><?php echo _l('cr_table_title'); ?></th>
                                    <th><?php echo _l('cr_table_project'); ?></th>
                                    <th><?php echo _l('cr_table_priority'); ?></th>
                                    <th><?php echo _l('cr_table_status'); ?></th>
                                    <th><?php echo _l('cr_table_requested_by'); ?></th>
                                    <th><?php echo _l('cr_table_date_created'); ?></th>
                                    <th><?php echo _l('options'); ?></th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php init_tail(); ?>

<script>
$(function() {
    initDataTable('.table-change-requests', admin_url + 'project_cr/table', [6], [6], {}, [5, 'desc']);
    
    // Load statistics
    loadStatistics();
    
    // Filter handlers
    $('#filter_status, #filter_priority').on('change', function() {
        $('.table-change-requests').DataTable().ajax.reload();
    });
});

function loadStatistics() {
    $.get(admin_url + 'project_cr/get_statistics', function(response) {
        if (response.success) {
            $('#total-crs').text(response.data.total);
            $('#draft-crs').text(response.data.draft);
            $('#submitted-crs').text(response.data.submitted);
            $('#approved-crs').text(response.data.approved);
            $('#rejected-crs').text(response.data.rejected);
            $('#implemented-crs').text(response.data.implemented);
        }
    }, 'json');
}
</script>
