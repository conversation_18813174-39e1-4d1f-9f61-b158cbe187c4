<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel_s">
                    <div class="panel-body">
                        <h4 class="no-margin">
                            <?php echo $title; ?>
                        </h4>
                        <hr class="hr-panel-heading" />
                        
                        <?php echo form_open(admin_url('project_cr/change_request' . (isset($change_request) ? '/' . $change_request->id : '')), ['id' => 'change-request-form']); ?>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="project_id" class="control-label">
                                        <?php echo _l('project'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select name="project_id" id="project_id" class="form-control selectpicker" data-live-search="true" required>
                                        <option value=""><?php echo _l('dropdown_non_selected_tex'); ?></option>
                                        <?php foreach ($projects as $project) { ?>
                                        <option value="<?php echo $project['id']; ?>" 
                                            <?php if (isset($change_request) && $change_request->project_id == $project['id']) echo 'selected'; ?>>
                                            <?php echo $project['name']; ?>
                                        </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="title" class="control-label">
                                        <?php echo _l('cr_title'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" id="title" name="title" class="form-control" 
                                           value="<?php echo isset($change_request) ? $change_request->title : ''; ?>" required>
                                    <small class="text-muted"><?php echo _l('cr_title_help'); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority" class="control-label"><?php echo _l('cr_priority'); ?></label>
                                    <select name="priority" id="priority" class="form-control selectpicker">
                                        <?php foreach (get_change_request_priorities() as $priority) { ?>
                                        <option value="<?php echo $priority['id']; ?>" 
                                            <?php if (isset($change_request) && $change_request->priority == $priority['id']) echo 'selected'; 
                                                  elseif (!isset($change_request) && $priority['id'] == 2) echo 'selected'; ?>>
                                            <?php echo $priority['name']; ?>
                                        </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="assigned_to" class="control-label"><?php echo _l('cr_assigned_to'); ?></label>
                                    <select name="assigned_to" id="assigned_to" class="form-control selectpicker" data-live-search="true">
                                        <option value=""><?php echo _l('not_assigned'); ?></option>
                                        <?php foreach ($staff_members as $staff) { ?>
                                        <option value="<?php echo $staff['staffid']; ?>" 
                                            <?php if (isset($change_request) && $change_request->assigned_to == $staff['staffid']) echo 'selected'; ?>>
                                            <?php echo $staff['firstname'] . ' ' . $staff['lastname']; ?>
                                        </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="estimated_cost" class="control-label"><?php echo _l('cr_estimated_cost'); ?></label>
                                    <input type="number" step="0.01" id="estimated_cost" name="estimated_cost" class="form-control" 
                                           value="<?php echo isset($change_request) ? $change_request->estimated_cost : ''; ?>">
                                    <small class="text-muted"><?php echo _l('cr_estimated_cost_help'); ?></small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="estimated_hours" class="control-label"><?php echo _l('cr_estimated_hours'); ?></label>
                                    <input type="number" step="0.25" id="estimated_hours" name="estimated_hours" class="form-control" 
                                           value="<?php echo isset($change_request) ? $change_request->estimated_hours : ''; ?>">
                                    <small class="text-muted"><?php echo _l('cr_estimated_hours_help'); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description" class="control-label">
                                        <?php echo _l('cr_description'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <textarea id="description" name="description" class="form-control" rows="4" required><?php echo isset($change_request) ? $change_request->description : ''; ?></textarea>
                                    <small class="text-muted"><?php echo _l('cr_description_help'); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="justification" class="control-label"><?php echo _l('cr_justification'); ?></label>
                                    <textarea id="justification" name="justification" class="form-control" rows="3"><?php echo isset($change_request) ? $change_request->justification : ''; ?></textarea>
                                    <small class="text-muted"><?php echo _l('cr_justification_help'); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="impact_analysis" class="control-label"><?php echo _l('cr_impact_analysis'); ?></label>
                                    <textarea id="impact_analysis" name="impact_analysis" class="form-control" rows="3"><?php echo isset($change_request) ? $change_request->impact_analysis : ''; ?></textarea>
                                    <small class="text-muted"><?php echo _l('cr_impact_analysis_help'); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (isset($change_request) && $change_request->status == 4) { ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="rejection_reason" class="control-label"><?php echo _l('cr_rejection_reason'); ?></label>
                                    <textarea id="rejection_reason" name="rejection_reason" class="form-control" rows="2" readonly><?php echo $change_request->rejection_reason; ?></textarea>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                        
                        <div class="btn-bottom-toolbar text-right">
                            <button type="submit" class="btn btn-info"><?php echo _l('submit'); ?></button>
                        </div>
                        
                        <?php echo form_close(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php init_tail(); ?>

<script>
$(function() {
    appValidateForm($('#change-request-form'), {
        project_id: 'required',
        title: 'required',
        description: 'required'
    });
});
</script>
