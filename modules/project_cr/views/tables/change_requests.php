<?php

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [
    'title',
    '(SELECT name FROM ' . db_prefix() . 'projects WHERE id = ' . db_prefix() . 'project_change_requests.project_id) as project_name',
    'priority',
    'status',
    'CONCAT(requested_staff.firstname, " ", requested_staff.lastname) as requested_by_name',
    'date_created',
];

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'project_change_requests';

$join = [
    'LEFT JOIN ' . db_prefix() . 'staff as requested_staff ON requested_staff.staffid = ' . db_prefix() . 'project_change_requests.requested_by',
];

$where = [];

// Filter by status
if ($this->input->post('filter_status') && $this->input->post('filter_status') != '') {
    array_push($where, 'AND status = ' . $this->db->escape_str($this->input->post('filter_status')));
}

// Filter by priority
if ($this->input->post('filter_priority') && $this->input->post('filter_priority') != '') {
    array_push($where, 'AND priority = ' . $this->db->escape_str($this->input->post('filter_priority')));
}

// Check permissions
if (!staff_can('view', 'project_change_requests')) {
    // Only show change requests created by or assigned to current user
    $staff_id = get_staff_user_id();
    array_push($where, 'AND (requested_by = ' . $staff_id . ' OR assigned_to = ' . $staff_id . ')');
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    db_prefix() . 'project_change_requests.id',
    'estimated_cost',
    'estimated_hours',
    'assigned_to',
]);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];
    
    // Title with link
    $title = '<a href="' . admin_url('project_cr/change_request/' . $aRow['id']) . '">' . $aRow['title'] . '</a>';
    $row[] = $title;
    
    // Project name
    $row[] = $aRow['project_name'];
    
    // Priority
    $row[] = format_change_request_priority($aRow['priority']);
    
    // Status
    $row[] = format_change_request_status($aRow['status']);
    
    // Requested by
    $row[] = $aRow['requested_by_name'];
    
    // Date created
    $row[] = _dt($aRow['date_created']);
    
    // Options
    $options = '<div class="tw-flex tw-items-center tw-space-x-2">';
    
    if (staff_can('view', 'project_change_requests') || can_view_change_request($aRow['id'])) {
        $options .= '<a href="' . admin_url('project_cr/change_request/' . $aRow['id']) . '" class="tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700">
            <i class="fa-regular fa-pen-to-square fa-lg"></i>
        </a>';
    }
    
    if (staff_can('delete', 'project_change_requests')) {
        $options .= '<a href="' . admin_url('project_cr/delete/' . $aRow['id']) . '" class="tw-mt-px tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700 _delete">
            <i class="fa-regular fa-trash-can fa-lg"></i>
        </a>';
    }
    
    $options .= '</div>';
    
    $row[] = $options;
    
    $output['aaData'][] = $row;
}
