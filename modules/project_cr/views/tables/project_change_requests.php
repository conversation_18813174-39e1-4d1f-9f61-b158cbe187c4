<?php

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [
    'title',
    'priority',
    'status',
    'CONCAT(requested_staff.firstname, " ", requested_staff.lastname) as requested_by_name',
    'date_created',
];

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'project_change_requests';

$join = [
    'LEFT JOIN ' . db_prefix() . 'staff as requested_staff ON requested_staff.staffid = ' . db_prefix() . 'project_change_requests.requested_by',
];

$where = [];

// Filter by project ID
$project_id = $this->input->post('project_id');
if ($project_id) {
    array_push($where, 'AND project_id = ' . (int)$project_id);
}

// Check permissions
if (!staff_can('view', 'project_change_requests')) {
    // Only show change requests created by or assigned to current user
    $staff_id = get_staff_user_id();
    array_push($where, 'AND (requested_by = ' . $staff_id . ' OR assigned_to = ' . $staff_id . ')');
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    db_prefix() . 'project_change_requests.id',
    'estimated_cost',
    'estimated_hours',
    'assigned_to',
]);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];
    
    // Title with link
    $title = '<a href="' . admin_url('project_cr/change_request/' . $aRow['id']) . '">' . $aRow['title'] . '</a>';
    $row[] = $title;
    
    // Priority
    $priority_colors = [1 => '#28a745', 2 => '#ffc107', 3 => '#fd7e14', 4 => '#dc3545'];
    $priority_names = [1 => 'Low', 2 => 'Medium', 3 => 'High', 4 => 'Critical'];
    $priority_color = isset($priority_colors[$aRow['priority']]) ? $priority_colors[$aRow['priority']] : '#6c757d';
    $priority_name = isset($priority_names[$aRow['priority']]) ? $priority_names[$aRow['priority']] : 'Unknown';
    $row[] = '<span class="label" style="background-color: ' . $priority_color . '">' . $priority_name . '</span>';

    // Status
    $status_colors = [1 => '#6c757d', 2 => '#007bff', 3 => '#28a745', 4 => '#dc3545', 5 => '#17a2b8'];
    $status_names = [1 => 'Draft', 2 => 'Submitted', 3 => 'Approved', 4 => 'Rejected', 5 => 'Implemented'];
    $status_color = isset($status_colors[$aRow['status']]) ? $status_colors[$aRow['status']] : '#6c757d';
    $status_name = isset($status_names[$aRow['status']]) ? $status_names[$aRow['status']] : 'Unknown';
    $row[] = '<span class="label" style="background-color: ' . $status_color . '">' . $status_name . '</span>';
    
    // Requested by
    $row[] = $aRow['requested_by_name'];
    
    // Date created
    $row[] = function_exists('_dt') ? _dt($aRow['date_created']) : date('Y-m-d H:i:s', strtotime($aRow['date_created']));
    
    // Options
    $options = '<div class="tw-flex tw-items-center tw-space-x-2">';

    // Always show edit link for now (can add permission checks later)
    $options .= '<a href="' . admin_url('project_cr/change_request/' . $aRow['id']) . '" class="tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700">
        <i class="fa-regular fa-pen-to-square fa-lg"></i>
    </a>';

    // Status update dropdown for authorized users
    if (function_exists('staff_can') && staff_can('edit', 'project_change_requests')) {
        $options .= '<div class="dropdown">
            <a href="#" class="tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700" data-toggle="dropdown">
                <i class="fa fa-cog fa-lg"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 2)">Submitted</a></li>
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 3)">Approved</a></li>
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 4)">Rejected</a></li>
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 5)">Implemented</a></li>
            </ul>
        </div>';
    }

    if (function_exists('staff_can') && staff_can('delete', 'project_change_requests')) {
        $options .= '<a href="' . admin_url('project_cr/delete/' . $aRow['id']) . '" class="tw-mt-px tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700 _delete">
            <i class="fa-regular fa-trash-can fa-lg"></i>
        </a>';
    }

    $options .= '</div>';
    
    $row[] = $options;
    
    $output['aaData'][] = $row;
}
