<?php

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [
    'title',
    'priority',
    'status',
    'CONCAT(requested_staff.firstname, " ", requested_staff.lastname) as requested_by_name',
    'date_created',
];

$sIndexColumn = 'id';
$sTable       = db_prefix() . 'project_change_requests';

$join = [
    'LEFT JOIN ' . db_prefix() . 'staff as requested_staff ON requested_staff.staffid = ' . db_prefix() . 'project_change_requests.requested_by',
];

$where = [];

// Filter by project ID
if (isset($project_id) && $project_id) {
    array_push($where, 'AND project_id = ' . (int)$project_id);
}

// Check permissions
if (!staff_can('view', 'project_change_requests')) {
    // Only show change requests created by or assigned to current user
    $staff_id = get_staff_user_id();
    array_push($where, 'AND (requested_by = ' . $staff_id . ' OR assigned_to = ' . $staff_id . ')');
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    db_prefix() . 'project_change_requests.id',
    'estimated_cost',
    'estimated_hours',
    'assigned_to',
]);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];
    
    // Title with link
    $title = '<a href="' . admin_url('project_cr/change_request/' . $aRow['id']) . '">' . $aRow['title'] . '</a>';
    $row[] = $title;
    
    // Priority
    $row[] = format_change_request_priority($aRow['priority']);
    
    // Status
    $row[] = format_change_request_status($aRow['status']);
    
    // Requested by
    $row[] = $aRow['requested_by_name'];
    
    // Date created
    $row[] = _dt($aRow['date_created']);
    
    // Options
    $options = '<div class="tw-flex tw-items-center tw-space-x-2">';
    
    if (staff_can('view', 'project_change_requests') || can_view_change_request($aRow['id'])) {
        $options .= '<a href="' . admin_url('project_cr/change_request/' . $aRow['id']) . '" class="tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700">
            <i class="fa-regular fa-pen-to-square fa-lg"></i>
        </a>';
    }
    
    // Status update dropdown for authorized users
    if (staff_can('edit', 'project_change_requests')) {
        $options .= '<div class="dropdown">
            <a href="#" class="tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700" data-toggle="dropdown">
                <i class="fa fa-cog fa-lg"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 2)">' . _l('cr_status_submitted') . '</a></li>
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 3)">' . _l('cr_status_approved') . '</a></li>
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 4)">' . _l('cr_status_rejected') . '</a></li>
                <li><a href="#" onclick="updateStatus(' . $aRow['id'] . ', 5)">' . _l('cr_status_implemented') . '</a></li>
            </ul>
        </div>';
    }
    
    if (staff_can('delete', 'project_change_requests')) {
        $options .= '<a href="' . admin_url('project_cr/delete/' . $aRow['id']) . '" class="tw-mt-px tw-text-neutral-500 hover:tw-text-neutral-700 focus:tw-text-neutral-700 _delete">
            <i class="fa-regular fa-trash-can fa-lg"></i>
        </a>';
    }
    
    $options .= '</div>';
    
    $row[] = $options;
    
    $output['aaData'][] = $row;
}
