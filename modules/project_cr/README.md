# Project Change Requests Module

A Perfex CRM module for managing change requests within projects.

## Features

- **Project Integration**: Adds a "Change Requests" tab to project views
- **Change Request Management**: Create, edit, and track change requests
- **Status Workflow**: Draft → Submitted → Approved/Rejected → Implemented
- **Priority Levels**: Low, Medium, High, Critical
- **Cost & Time Estimation**: Track estimated costs and hours for changes
- **Comments System**: Add comments to change requests for collaboration
- **Statistics Dashboard**: View change request statistics by project
- **Permissions**: Granular staff permissions for CR management
- **Responsive Design**: Works on desktop and mobile devices

## Installation

1. Upload the `project_cr` folder to your Perfex CRM `modules` directory
2. Go to Admin → Modules in your Perfex CRM
3. Find "Project Change Requests" and click "Install"
4. Activate the module

## Usage

### Creating Change Requests

1. Navigate to any project
2. Click on the "Change Requests" tab
3. Click "New Change Request" button
4. Fill in the required information:
   - Title (required)
   - Description (required)
   - Priority level
   - Estimated cost and hours
   - Justification and impact analysis

### Managing Change Requests

- **View All**: Go to Projects → Change Requests to see all CRs
- **Filter**: Filter by status, priority, or project
- **Update Status**: Use the status dropdown to move CRs through workflow
- **Edit**: Click on any CR title to edit details
- **Delete**: Use the delete button (requires permission)

### Status Workflow

1. **Draft**: Initial state when created
2. **Submitted**: Ready for review
3. **Approved**: Approved for implementation
4. **Rejected**: Not approved (with reason)
5. **Implemented**: Change has been completed

### Permissions

The module adds these permission categories:
- **View**: View change requests
- **Create**: Create new change requests
- **Edit**: Edit existing change requests
- **Delete**: Delete change requests

## Database Tables

The module creates three tables:
- `tblproject_change_requests`: Main CR data
- `tblproject_cr_comments`: Comments on CRs
- `tblproject_cr_attachments`: File attachments (future feature)

## Configuration

Module options (can be configured in Admin → Settings):
- Auto-assign CRs to project manager
- Email notifications for status changes
- Require approval workflow

## Customization

### Adding Custom Fields

To add custom fields to change requests:

1. Modify the database table in `install.php`
2. Update the model in `models/Project_cr_model.php`
3. Add form fields in `views/change_request.php`
4. Update language files as needed

### Custom Status Workflow

To modify the status workflow:

1. Update the `get_change_request_statuses()` function in `project_cr.php`
2. Modify the status dropdown in views
3. Update language files for new status names

## Hooks and Filters

The module provides these hooks:
- `change_request_created`: Fired when a new CR is created
- `change_request_updated`: Fired when a CR is updated
- `change_request_deleted`: Fired when a CR is deleted
- `before_change_request_deleted`: Fired before CR deletion

## Support

For support and customization requests, please contact your Perfex CRM developer.

## Version History

- **1.0.0**: Initial release with core functionality

## License

This module is provided as-is for Perfex CRM installations.
