<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Project_cr extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('project_cr/project_cr_model');
        $this->load->model('projects_model');
        $this->load->model('staff_model');
    }

    /**
     * List all change requests
     */
    public function index()
    {
        if (staff_cant('view', 'project_change_requests')) {
            access_denied('Change Requests');
        }

        $data['title'] = _l('change_requests');
        $this->load->view('project_cr/manage', $data);
    }

    /**
     * Create or edit change request
     */
    public function change_request($id = '')
    {
        if ($this->input->post()) {
            $data = $this->input->post();
            
            if ($id == '') {
                if (staff_cant('create', 'project_change_requests')) {
                    access_denied('Change Requests');
                }
                
                $data['requested_by'] = get_staff_user_id();
                $data['date_created'] = date('Y-m-d H:i:s');
                
                $id = $this->project_cr_model->add($data);
                if ($id) {
                    set_alert('success', _l('added_successfully', _l('change_request')));
                    redirect(admin_url('project_cr/change_request/' . $id));
                }
            } else {
                if (staff_cant('edit', 'project_change_requests')) {
                    access_denied('Change Requests');
                }
                
                $data['date_updated'] = date('Y-m-d H:i:s');
                
                $success = $this->project_cr_model->update($data, $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('change_request')));
                }
                redirect(admin_url('project_cr/change_request/' . $id));
            }
        }

        if ($id == '') {
            $title = _l('add_new', _l('change_request'));
        } else {
            $data['change_request'] = $this->project_cr_model->get($id);
            if (!$data['change_request']) {
                blank_page(_l('change_request_not_found'));
            }
            $title = _l('edit', _l('change_request'));
        }

        $data['projects'] = $this->projects_model->get();
        $data['staff_members'] = $this->staff_model->get();
        $data['title'] = $title;
        
        $this->load->view('project_cr/change_request', $data);
    }

    /**
     * Delete change request
     */
    public function delete($id)
    {
        if (staff_cant('delete', 'project_change_requests')) {
            access_denied('Change Requests');
        }

        if (!$id) {
            redirect(admin_url('project_cr'));
        }

        $response = $this->project_cr_model->delete($id);
        if ($response == true) {
            set_alert('success', _l('deleted', _l('change_request')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('change_request_lowercase')));
        }
        redirect(admin_url('project_cr'));
    }

    /**
     * Change request table for datatables
     */
    public function table()
    {
        if (staff_cant('view', 'project_change_requests')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path('project_cr', 'tables/change_requests'));
    }

    /**
     * Update change request status
     */
    public function update_status()
    {
        if ($this->input->post()) {
            $id = $this->input->post('id');
            $status = $this->input->post('status');
            
            if (staff_cant('edit', 'project_change_requests')) {
                echo json_encode(['success' => false, 'message' => _l('access_denied')]);
                return;
            }

            $data = ['status' => $status, 'date_updated' => date('Y-m-d H:i:s')];
            
            if ($status == 3) { // Approved
                $data['approved_by'] = get_staff_user_id();
                $data['approved_date'] = date('Y-m-d H:i:s');
            } elseif ($status == 5) { // Implemented
                $data['implementation_date'] = date('Y-m-d H:i:s');
            }

            $success = $this->project_cr_model->update($data, $id);
            
            echo json_encode([
                'success' => $success,
                'message' => $success ? _l('updated_successfully', _l('change_request')) : _l('something_went_wrong')
            ]);
        }
    }

    /**
     * Add comment to change request
     */
    public function add_comment()
    {
        if ($this->input->post()) {
            $data = [
                'cr_id' => $this->input->post('cr_id'),
                'staff_id' => get_staff_user_id(),
                'comment' => $this->input->post('comment'),
                'date_created' => date('Y-m-d H:i:s')
            ];

            $success = $this->project_cr_model->add_comment($data);

            echo json_encode([
                'success' => $success,
                'message' => $success ? _l('comment_added_successfully') : _l('something_went_wrong')
            ]);
        }
    }

    /**
     * Add change request from project tab
     */
    public function add_project_cr()
    {
        if ($this->input->post()) {
            $data = $this->input->post();

            if (staff_cant('create', 'project_change_requests')) {
                echo json_encode(['success' => false, 'message' => _l('access_denied')]);
                return;
            }

            $data['requested_by'] = get_staff_user_id();
            $data['date_created'] = date('Y-m-d H:i:s');
            $data['status'] = 1; // Draft

            $id = $this->project_cr_model->add($data);

            echo json_encode([
                'success' => $id ? true : false,
                'message' => $id ? _l('added_successfully', _l('change_request')) : _l('something_went_wrong'),
                'id' => $id
            ]);
        }
    }

    /**
     * Project change requests table
     */
    public function project_table($project_id)
    {
        if (staff_cant('view', 'project_change_requests')) {
            ajax_access_denied();
        }

        // Pass project_id to the table view
        $_POST['project_id'] = $project_id;
        $this->app->get_table_data(module_views_path('project_cr', 'tables/project_change_requests'));
    }

    /**
     * Get statistics for all change requests
     */
    public function get_statistics()
    {
        if (staff_cant('view', 'project_change_requests')) {
            echo json_encode(['success' => false, 'message' => _l('access_denied')]);
            return;
        }

        $stats = $this->project_cr_model->get_statistics();

        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get statistics for project change requests
     */
    public function get_project_statistics($project_id)
    {
        if (staff_cant('view', 'project_change_requests')) {
            echo json_encode(['success' => false, 'message' => _l('access_denied')]);
            return;
        }

        $stats = $this->project_cr_model->get_statistics($project_id);

        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
    }
}
